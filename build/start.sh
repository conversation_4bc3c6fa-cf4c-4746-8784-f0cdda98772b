# fix java.nio.file.InvalidPathException: Malformed input or input contains unmappable chacraters
export LC_CTYPE="en_US.UTF-8"
CDIR=$(pwd)
sudo mkdir -p /dcas/storage/update
sudo mkdir -p /dcas/docker/cert/
sudo chmod -R 777 /dcas
sudo mkdir -p /opt/market/resource/
sudo mkdir -p /opt/market/apps/
sudo chmod -R 777 /opt/market
sudo chmod -R 777 /opt/mc/common/data/storage/metis/
sudo chmod +x /opt/mc/apps/dcas_se/current/cip.sh
sudo chmod +x /opt/mc/apps/dcas_se/current/update.sh
cd $CDIR

java -Xms256m -Xmx2g -Xmn256m -Dfile.encoding=UTF8 -Duser.timezone=Asia/Shanghai -DMCDATASOURCE_HOME=/opt/mc/common/data/storage/mcdatasource -DMCDATASOURCE_VERSION=******* -Dmetis.app.path=/opt/mc/common/data/storage/metis -jar -Dspring.config.location=./application.yml dcas-admin.jar