package com.dcas.common.domain.entity;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dcas.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 合法合规业务表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
@TableName("co_legal")
public class CoLegal implements Serializable {
    /**
     * 合规id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String legalId;

    /**
     * 作业id
     */
    private String operationId;

    /**
     * 分类id
     */
    private Long labelId;

    /**
     * 法律条文id
     */
    private Integer lawId;

    /**
     * 系统id
     */
    private Long systemId;

    /**
     * 法律文件名称
     */
    @ApiModelProperty(value = "文件名称")
    @Excel(name = "文件名称")
    private String lawName;

    /**
     * 条文编号
     */
    @ApiModelProperty(value = "条文编号")
    @Excel(name = "条文编号")
    private Integer itemNum;

    /**
     * 条文内容
     */
    @ApiModelProperty(value = "条文要求")
    @Excel(name = "条文内容")
    private String itemContent;

    /**
     * 条文解释
     */
    @ApiModelProperty(value = "条文解释")
    @Excel(name = "条文解释")
    private String itemExplain;

    /**
     * 现状描述
     */
    @ApiModelProperty(value = "现状描述")
    @Excel(name = "现状描述")
    private String remark;

    /**
     * 符合性分析
     */
    @ApiModelProperty(value = "符合性分析结果")
    @Excel(name = "符合性分析结果")
    private String result;

    /**
     * 风险等级
     */
    @ApiModelProperty(value = "风险等级")
    @Excel(name = "风险等级")
    private String riskLevel;

    /**
     * 款号
     */
    private Integer kuanNum;

    /**
     * 项号
     */
    private Integer xiangNum;

    /**
     * 目号
     */
    private Integer muNum;

    /**
     * 法条编码
     */
    private String articleCode;

    /**
     * 法律条文ids
     */
    private String itemId;

    /**
     * 各系统符合性分析结果
     */
    private String systemResult;

    @ApiModelProperty(value = "分数")
    private BigDecimal score;

    @ApiModelProperty(value = "不符合现状描述")
    private String incompatible;

    @TableField(exist = false)
    @ApiModelProperty(value = "风险描述")
    private String riskDesc;

    @TableField(exist = false)
    @ApiModelProperty(value = "业务系统符合情况以及风险分析情况")
    private List<CoSystemResult> coSystemResultList;

    @JsonIgnore
    public String getDesc(){
        String imcompatible = getIncompatible();
        if (StrUtil.isEmpty(incompatible)){
            return getRemark();
        }
        if (imcompatible.startsWith(StrPool.LF)){
            imcompatible = CharSequenceUtil.replaceFirst(imcompatible, StrPool.LF, "");
        }
        if (imcompatible.endsWith(StrPool.LF)){
            imcompatible = CharSequenceUtil.replaceLast(imcompatible, StrPool.LF, "");
        }
        if (CharSequenceUtil.isEmpty(getRemark())) {
            return imcompatible;
        } else {
            String desc = getRemark();
            if (desc.startsWith(StrPool.LF)){
                desc = CharSequenceUtil.replaceFirst(desc, StrPool.LF, "");
            }
            if (desc.endsWith(StrPool.LF)){
                desc = CharSequenceUtil.replaceLast(desc, StrPool.LF, "");
            }
            if (CharSequenceUtil.isEmpty(getIncompatible())){
                return desc;
            }
            return desc + StrPool.LF + imcompatible;
        }
    }
    private static final long serialVersionUID = 1L;
}