package com.dcas.common.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import lombok.*;

/**
 * <p>
 * 业务系统结果表，包含合规符合性结果和能力符合性结果
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-25
 */
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("co_system_result")
@AllArgsConstructor
@NoArgsConstructor
public class CoSystemResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 作业ID
     */
    @TableField("operation_id")
    private String operationId;

    /**
     * 业务系统ID
     */
    @TableField("system_id")
    private Long systemId;

    /**
     * 业务系统名
     */
    @TableField(exist = false)
    private String systemName;

    /**
     * 业务系统结果
     */
    @TableField("system_result")
    private String systemResult;

    /**
     * 合法合规业务表ID或者现状核验业务表ID
     */
    @TableField("rel_id")
    private String relId;

    /**
     * 类型：1-合规 2-能力
     */
    @TableField("type")
    private Integer type;

    /**
     * 现状描述
     */
    @TableField("description")
    private String description;

    /**
     * 不符合现状描述
     */
    @TableField("incompatible")
    private String incompatible;

    /**
     * 知识库风险分析
     */
    @TableField("risk_desc")
    private String riskDesc;

    /**
     * AI风险分析结果
     */
    @TableField("ai_risk_desc")
    private String aiRiskDesc;

    /**
     * 关联llmTask任务状态
     */
    @TableField(exist = false)
    private Integer status;
}
