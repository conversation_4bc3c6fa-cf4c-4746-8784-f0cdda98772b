package com.dcas.common.domain.entity;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 现状核验业务表
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
@TableName("co_verification")
public class CoVerification implements Serializable {
    /**
     * 核验id
     */
    @ApiModelProperty(value = "现状核验id")
    @TableId(type = IdType.ASSIGN_ID)
    private String verificationId;

    /**
     * 作业id
     */
    @ApiModelProperty(value = "作业id")
    private String operationId;

    /**
     * 分类id
     */
    @ApiModelProperty(value = "分类id")
    private Long labelId;

    /**
     * 模板编码
     */
    @ApiModelProperty(value = "BP编码")
    private String bpCode;

    /**
     * 能力项/GP维度
     */
    @ApiModelProperty(value = "能力项")
    private String gpDimension;

    /**
     * 能力要求（标准条款要求）
     */
    @ApiModelProperty(value = "能力要求")
    private String standardProvision;

    /**
     * 现状描述（过程域描述）
     */
    @ApiModelProperty(value = "现状描述")
    private String description;

    /**
     * 符合性判断结果
     */
    @ApiModelProperty(value = "符合性判断结果")
    private String result;

    /**
     * 模板名称
     */
    @ApiModelProperty(value = "模板名称")
    private String modelName;

    /**
     * 能力级别
     */
    @ApiModelProperty(value = "能力级别")
    private String level;

    /**
     * 视图类型
     */
    @ApiModelProperty(value = "视图类型")
    private String type;

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    private Integer sort;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "系统id")
    private Long systemId;

    @ApiModelProperty(value = "各系统符合性结果")
    private String systemResult;

    @ApiModelProperty(value = "调节因子")
    private Double regulatoryFactor;

    @ApiModelProperty(value = "阶段")
    private String stage;

    @ApiModelProperty(value = "过程")
    private String process;

    @ApiModelProperty(value = "能力模板ID")
    private Integer analysisId;

    @ApiModelProperty(value = "文件ID")
    private Integer standardId;

    @ApiModelProperty(value = "标准文件名")
    @TableField(exist = false)
    private String standardFile;

    @ApiModelProperty(value = "分类")
    private String classify;
    @ApiModelProperty(value = "分数")
    private BigDecimal score;

    @ApiModelProperty(value = "不符合现状描述")
    private String incompatible;


    @ApiModelProperty(value = "核查项符合率")
    private BigDecimal itemRate;

    /**
     * 能力要求（标准条款要求）
     */
    @ApiModelProperty(value = "安全能力维度")
    @TableField(exist = false)
    private String dimension;

    @ApiModelProperty(value = "风险描述")
    @TableField(exist = false)
    private String riskDesc;

    @TableField(exist = false)
    @ApiModelProperty(value = "业务系统符合情况以及风险分析情况")
    private List<CoSystemResult> coSystemResultList;

    @JsonIgnore
    public Double getBpCodeDouble(){
        return Double.parseDouble(bpCode.replace(StrPool.DOT, ""));
    }
    @JsonIgnore
    public String getDesc(){
        String imcompatible = getIncompatible();
        if (StrUtil.isEmpty(incompatible))
            return getDescription();
        if (imcompatible.startsWith(StrPool.LF)){
            imcompatible = CharSequenceUtil.replaceFirst(imcompatible, StrPool.LF, "");
        }
        if (imcompatible.endsWith(StrPool.LF)){
            imcompatible = CharSequenceUtil.replaceLast(imcompatible, StrPool.LF, "");
        }
        if (CharSequenceUtil.isEmpty(getDescription())) {
            return imcompatible;
        } else {
            String desc = getDescription();
            if (desc.startsWith(StrPool.LF)){
                desc = CharSequenceUtil.replaceFirst(desc, StrPool.LF, "");
            }
            if (desc.endsWith(StrPool.LF)){
                desc = CharSequenceUtil.replaceLast(desc, StrPool.LF, "");
            }
            if (CharSequenceUtil.isEmpty(getIncompatible())){
                return desc;
            }
            return desc + StrPool.LF + imcompatible;
        }
    }
    private static final long serialVersionUID = 1L;
}