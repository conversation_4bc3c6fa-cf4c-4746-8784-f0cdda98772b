package com.dcas.common.enums;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.mchz.mcdatasource.core.DataBaseType;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 数据源类型枚举
 *
 * schema表示数据库使用场景：
 * add      1 << 0 = 1   0001
 * scan     1 << 1 = 2   0010
 * import   1 << 2 = 4   0100
 * file     1 << 3 = 8   1000
 * assetRelation 1 << 4 = 16 0001 0000
 *
 * <AUTHOR>
 * @date Created in 2018/8/21 下午6:27
 */
@AllArgsConstructor
public enum DataSourceType {
    ORACLE(0L, "Oracle", true, SourceType.RELATION, Dialect.ORACLE, 23, DataBaseType.ORACLE),
    SQL_SERVER(1L, "SQL Server", true, SourceType.RELATION, 23, DataBaseType.MSSQL),
    MYSQL(2L, "MySQL", true, SourceType.RELATION, Dialect.MYSQL,23, DataBaseType.MYSQL),
    MYSQL5(121L, "MYSQL5", true, SourceType.RELATION, Dialect.MYSQL,23, DataBaseType.MYSQL_5),
    MYSQL_TD(180L, "MySQL_TD", true, SourceType.RELATION, Dialect.MYSQL, 23, DataBaseType.MYSQL_TD),
    GOLDEN_DB(181L, "GOLDEN DB", true, SourceType.RELATION, Dialect.MYSQL, 23, DataBaseType.GOLDEN_DB),
    DB2(8L, "DB2", true, SourceType.RELATION, 23, DataBaseType.DB2),
    POSTGRE_SQL(3L, "PostgreSQL", true, SourceType.RELATION, 7, DataBaseType.PGSQL),
    PGSQL_14(162L, "PostgreSQL 14", true, SourceType.RELATION, 7, DataBaseType.PGSQL_14),
    GREENPLUM(4L, "Greenplum", true, SourceType.RELATION, 1, DataBaseType.GREENPLUM),
    HIVE(5L, "Hive", true, SourceType.BIGDATA, 11, DataBaseType.HIVE),
    HBASE(7L, "hbase", true, SourceType.BIGDATA, 11, DataBaseType.HBASE),
    INFORMIX(10L, "informix", false, SourceType.RELATION, 7, DataBaseType.INFORMIX),
    RDS_MYSQL(12L, "阿里云RDS - MySQL", false, SourceType.RDS, 1, DataBaseType.RDS_MYSQL),
    RDS_MYSQL5(15L, "阿里云RDS - MySQL5", false, SourceType.RDS, 1, DataBaseType.RDS_MYSQL),
    RDS_POSTGRE_SQL(13L, "阿里云RDS - PostgreSQL", false, SourceType.RDS, 1, DataBaseType.RDS_PGSQL),
    RDS_SQL_SERVER(14L, "阿里云RDS - SQL Server", false, SourceType.RDS, 1, DataBaseType.RDS_MSSQL),
    MARIADB(16L, "MariaDB", false, SourceType.RELATION, 1, DataBaseType.MARIADB),
    HIVE_CDH6(20L, "Hive CDH6", true, SourceType.BIGDATA, 9, DataBaseType.HIVE_CDH634),
    DM(40L, "达梦", false, SourceType.RELATION, 1, DataBaseType.DM),
    K_DB(101L, "Inspur K-DB", false, SourceType.RELATION, 1, DataBaseType.KDB),
    GAUSS200(104L, "GaussDB 200", false, SourceType.RELATION, 7, DataBaseType.GAUSS200),
    GAUSS100(177L, "Gauss100", true, SourceType.RELATION, 7, DataBaseType.GAUSS100),
    OPENGAUSS(135L, "OpenGauss", false, SourceType.RELATION, 7, DataBaseType.OPENGAUSS),
    INCEPTOR(105L, "TRANSWARP INCEPTOR", true, SourceType.BIGDATA, 9, DataBaseType.HIVE_TDH6),
    HIVE_FHD6(106L, "FusionInsight Hive", true, SourceType.BIGDATA, 9, DataBaseType.HIVE_FHD653),
    PRESTO(107L, "Presto", true, SourceType.BIGDATA, 9, DataBaseType.PRESTO),
    AS_400(111L, "As400", true, SourceType.RELATION, 7, DataBaseType.AS400),
    ODPS(103L, "MaxCompute", true, SourceType.BIGDATA, 1, DataBaseType.ODPS),
    MONGODB(6L, "MongoDB", true, SourceType.BIGDATA, 3, DataBaseType.MONGODB),
    HIVE_HDP3(108L, "Hive HDP3", true, SourceType.BIGDATA, 9, DataBaseType.HIVE_HDP315),
    GBASE8A(109L, "Gbase8a", false, SourceType.RELATION, 7, DataBaseType.GBASE8A),
    KINGBASE(110L, "Kingbase", false, SourceType.RELATION, 7, DataBaseType.KINGBASE),
    KINGBASE8(113L, "Kingbase8", false, SourceType.RELATION, 7, DataBaseType.KINGBASE8),
    KINGBASE86(144L, "Kingbase86", false, SourceType.RELATION, 7, DataBaseType.KINGBASE86),
    TIDB(112L, "TIDB", false, SourceType.RELATION, 7, DataBaseType.TIDB),
    CACHE_2010(120L, "CACHE 2010", false, SourceType.RELATION, 7, DataBaseType.CACHE_2010),
    CACHE_2016(122L, "CACHE 2016", false, SourceType.RELATION, 7, DataBaseType.CACHE_2016),
    SYBASE(9L, "Sybase", false, SourceType.RELATION, 2, DataBaseType.SYBASE),
    OCEAN_BASE_MYSQL(132L, "OceanBase MySQL", false, SourceType.RELATION, 7, DataBaseType.OCEANBASE),
    OCEAN_BASE_ORACLE(133L, "OceanBase Oracle", false, SourceType.RELATION, 7, DataBaseType.OCEANBASE_ORACLE),
    FTP(140L, "FTP", false, null, 0, null),
    SMB(141L, "SMB", false, null, 0, null),
    NFS(142L, "NFS", false, null, 0, null),
    POLAR_MYSQL5(201L, "PolarDB MySQL5", true, SourceType.RELATION, 7, DataBaseType.POLAR_MYSQL),
    POLAR_MYSQL(157L, "PolarDB MySQL", true, SourceType.RELATION, 7, DataBaseType.POLAR_MYSQL),
    POLAR_ORACLE(158L, "PolarDB Oracle", true, SourceType.RELATION, 7, DataBaseType.POLAR_ORACLE),
    POLAR_PGSQL(168L, "PolarDB PostgreSQL", true, SourceType.RELATION, 7, DataBaseType.POLAR_PGSQL),


    OTHER(-1L, "其它", false, SourceType.OTHER, 0, null);

    @Getter
    private final Long code;

    @Getter
    private final String name;

    @Getter
    private final boolean additional;

    @Getter
    private final SourceType type;

    private final Dialect dialect;
    /**
     * kerberos 导入 发现 新增
     */
    private final int scheme;
    @Getter
    private final DataBaseType dataBaseType;

    DataSourceType(Long code, String name, boolean additional, SourceType type, int scheme,
                   DataBaseType dataBaseType) {
        this.code = code;
        this.name = name;
        this.additional = additional;
        this.type = type;
        this.dialect = Dialect.OTHER;
        this.scheme = scheme;
        this.dataBaseType = dataBaseType;
    }

    public final Dialect getDefaultDialect() {
        return dialect;
    }

    // static *******************************************************************************************************

    private static final Map<String, DataSourceType> CACHE_MAP = new ConcurrentHashMap<>();

    static {
        for (DataSourceType type : DataSourceType.values()) {
            CACHE_MAP.put(type.getCode().toString(), type);
            DataBaseType dataBaseType = type.dataBaseType;
            if (Objects.nonNull(dataBaseType)) {
                CACHE_MAP.put(dataBaseType.pluginId, type);
            }
        }
    }

    /**
     * 返回枚举类型
     *
     * @param name 值
     * @return 枚举类型
     */
    public static DataSourceType getType(String name) {
        if (null == name) {
            return null;
        }
        return CACHE_MAP.get(name);
    }

    /**
     * 返回枚举类型
     *
     * @param code 值
     * @return 枚举类型
     */
    public static DataSourceType getType(Integer code) {
        return getType(null == code ? null : code.toString());
    }

    /**
     * 返回枚举类型
     *
     * @param name 值
     * @return 枚举类型
     */
    public static DataSourceType getType(Object name) {
        if (null == name) {
            return null;
        }
        return getType(name.toString());
    }

    public static DataSourceType getTypeByName(String name) {
        if (null == name) {
            return null;
        }
        for (DataSourceType value : values()) {
            if (Objects.equals(value.name, name)) {
                return value;
            }
        }
        return null;
    }

    public static List<DataSourceType> getSchemeType(String scheme) {
        if (StrUtil.isNullOrUndefined(scheme)) {
            return Lists.newArrayList(values());
        }
        final int i = SchemeType.of(scheme, SchemeType.DEFAULT).getMark();
        return Arrays.stream(values()).filter(v -> (v.scheme & i) > 0).collect(Collectors.toList());
    }

    public boolean isSchemeType(SchemeType schemeType) {
        return (scheme & schemeType.getMark()) > 0;
    }
}
