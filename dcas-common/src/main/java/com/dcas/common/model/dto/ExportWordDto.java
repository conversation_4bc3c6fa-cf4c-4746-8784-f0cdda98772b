package com.dcas.common.model.dto;

import com.dcas.common.enums.LabelEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * 导出word(适用于数据权限)
 *
 * <AUTHOR>
 * @Date 2022/10/21 15:22
 * @ClassName ExportWordDto
 */
@ApiModel("导出word入参")
@Data
public class ExportWordDto implements Serializable {
    /**
     * 作业id
     */
    @ApiModelProperty(value = "作业id")
    @NotBlank(message = "作业id不能为空")
    private String operationId;

    /**
     * 图片列表
     */
    @ApiModelProperty(value = "图片列表")
    private List<ExportWordChart> chartList;


    /**
     * 通用模板中需要排除的内容
     */
    @ApiModelProperty(value = "通用模板中需要排除的内容，后端使用")
    private Set<LabelEnum> needExcludeContent;

}
