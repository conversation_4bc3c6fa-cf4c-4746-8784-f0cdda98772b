package com.dcas.common.model.vo;

import com.dcas.common.domain.entity.CoSystemResult;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/8/1 10:03
 * @since 1.1.0
 */
@Data
@Builder
public class OptimalFormDTO {

    private String result;

    private String systemResult;

    private String description;

    private String incompatible;

    private Long systemId;

    private String riskLevel;

    private BigDecimal score;
}
