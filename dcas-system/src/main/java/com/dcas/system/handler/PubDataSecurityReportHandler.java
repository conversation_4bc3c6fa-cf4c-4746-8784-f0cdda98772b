package com.dcas.system.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.domain.entity.*;
import com.dcas.common.enums.*;
import com.dcas.common.mapper.*;
import com.dcas.common.model.dto.BusSystemAuthorityOverviewDTO;
import com.dcas.common.model.dto.ExportWordChart;
import com.dcas.common.model.dto.OperationIdDto;
import com.dcas.common.model.req.SecurityReportCreateReq;
import com.dcas.common.model.vo.*;
import com.dcas.common.utils.Arith;
import com.dcas.common.utils.spring.SpringUtils;
import com.dcas.market.app.service.IAppService;
import com.dcas.system.service.CoAuthorityService;
import com.dcas.system.service.ScanTaskService;
import com.dcas.system.service.impl.SecurityOperationServiceImpl;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.deepoove.poi.plugin.toc.TOCRenderPolicy;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2024/6/26 17:23
 * @since 1.6.6
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PubDataSecurityReportHandler extends SecurityReportHandler{
    private final CoProjectMapper coProjectMapper;
    private final PreSourceConfigMapper preSourceConfigMapper;
    private final SecurityProcessLabelMapper securityProcessLabelMapper;
    private final CoInventoryMapper coInventoryMapper;
    private final IAppService iAppService;
    private final SecurityOperationMapper securityOperationMapper;
    private final SecurityOperationServiceImpl securityOperationService;

    @Override
    public SecurityReportType getType() {
        return SecurityReportType.PUB_DATA;
    }

    @Override
    @SneakyThrows
    public void export(SecurityReportCreateReq req) {
        SecurityOperation securityOperation = req.getSecurityOperation();
        ClassPathResource classPathResource = new ClassPathResource("template/pubDataSecurityTemplate.docx");

        //数据模型
        Map<String, Object> model = new HashMap<>();
        model.put("operationName", securityOperation.getName());
        String operationId = String.valueOf(securityOperation.getId());
        CoProject coProject = coProjectMapper.selectById(securityOperation.getProjectId());

        // 基础属性
        model.put("companyName", "美创科技");
        model.put("operationName", securityOperation.getName());
        model.put("createTime", DateUtil.formatChineseDate(securityOperation.getCreateTime(), false, false));
        model.put("finishTime", DateUtil.formatChineseDate(securityOperation.getUpdateTime(), false, false));
        model.put("customName", coProject.getCustomerName());
        model.put("operationMember", securityOperation.getExecutor());

        List<String> relatedSystemList = preSourceConfigMapper.getBusSystemByOperationId(
            String.valueOf(securityOperation.getId()));
        model.put("relatedSystem", CharSequenceUtil.join("、", relatedSystemList));

        // 图片赋值
        putModelPicture(req.getChartList(), model);

        //评估内容
        List<Integer> serviceContentList = JSON.parseArray("[" + securityOperation.getServiceContent() + "]", Integer.class);
        // 将检测能力添加到评估内容列表中去
        List<PreSourceConfig> preSourceConfigList = preSourceConfigMapper.selectList(new QueryWrapper<PreSourceConfig>().eq("operation_id", operationId).isNotNull("ability_module"));
        for (PreSourceConfig preSourceConfig : preSourceConfigList) {
            if (preSourceConfig.getAbilityModule() == null){
                continue;
            }
            if (AbilityType.contains(preSourceConfig.getAbilityModule(),
                AbilityType.DESENSITIZATION) && !serviceContentList.contains(AbilityType.DESENSITIZATION.getCode())) {
                serviceContentList.add(AbilityType.DESENSITIZATION.getCode());
            }
            if (AbilityType.contains(preSourceConfig.getAbilityModule(),
                AbilityType.ENCRYPT) && !serviceContentList.contains(AbilityType.ENCRYPT.getCode())) {
                serviceContentList.add(AbilityType.ENCRYPT.getCode());
            }
            if (AbilityType.contains(preSourceConfig.getAbilityModule(),
                AbilityType.PCAP) && !serviceContentList.contains(AbilityType.PCAP.getCode())) {
                serviceContentList.add(AbilityType.PCAP.getCode());
            }
        }
        // 数据安全能力评估详情、数据安全制度详情
        List<SecurityLabelItemReportVO> resultList = securityProcessLabelMapper.selectLabelItemBySecurityId(securityOperation.getId());
        dataSafetyAbilityDetails(resultList, model, req.getChartList(), securityOperation, serviceContentList);

        // 基础评估
        baseAssessment(model, req.getChartList(), serviceContentList, operationId, coProject);
        // 脱敏加密检测分析
        compareAnalysis(model, serviceContentList, operationId);
        // 总结
        summary(model, serviceContentList, securityOperation.getId());

        try (InputStream inputStream = classPathResource.getInputStream()) {
            LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
            TOCRenderPolicy tocPolicy = new TOCRenderPolicy();
            Configure config = Configure.builder().bind("busSystemAuthorityList", policy)
                .bind("userAssetAuthorityList",policy)
                .bind("detectionRecordList",policy)
                .bind("dmDiffList", policy)
                .bind("dmList", policy)
                .bind("epList", policy)
                .bind("epDiffList", policy)
                .bind("tocContents", tocPolicy)
                .build();
            String realFileName = String.format("%s报告.docx", securityOperation.getName());

            write(inputStream, req.getResponse(), config, model, realFileName);
        }
    }

    protected void putModelPicture(List<ExportWordChart> chartList, Map<String, Object> model) {
        //敏感数据占比图表 从入参中获取
        List<ExportWordChart> userAddChartList =
            chartList.stream().filter(s -> "用户权限分类统计结果(新增)".equals(s.getName())).collect(Collectors.toList());
        List<ExportWordChart> userDeleteDataChartList =
            chartList.stream().filter(s -> "用户权限分类统计结果(删除数据)".equals(s.getName())).collect(Collectors.toList());
        List<ExportWordChart> userDeleteTableChartList =
            chartList.stream().filter(s -> "用户权限分类统计结果(删除表)".equals(s.getName())).collect(Collectors.toList());
        List<ExportWordChart> userUpdateChartList =
            chartList.stream().filter(s -> "用户权限分类统计结果(修改)".equals(s.getName())).collect(Collectors.toList());
        List<ExportWordChart> userQueryChartList =
            chartList.stream().filter(s -> "用户权限分类统计结果(查询)".equals(s.getName())).collect(Collectors.toList());

        Map<String, String> imageBase64Data2 = getPicture(userAddChartList);
        Map<String, String> imageBase64Data3 = getPicture(userDeleteDataChartList);
        Map<String, String> imageBase64Data4 = getPicture(userDeleteTableChartList);
        Map<String, String> imageBase64Data5 = getPicture(userUpdateChartList);
        Map<String, String> imageBase64Data6 = getPicture(userQueryChartList);


        putImage("userAuthorityTypeAddImg", imageBase64Data2, model);
        putImage("userAuthorityTypeDeleteImg", imageBase64Data3, model);
        putImage("userAuthorityTypeDropImg", imageBase64Data4, model);
        putImage("userAuthorityTypeUpdateImg", imageBase64Data5, model);
        putImage("userAuthorityTypeSelectImg", imageBase64Data6, model);

    }

    private void summary(Map<String, Object> model, List<Integer> serviceContentList, Integer operationId) {
        SecurityCategoryResultVO vo = securityOperationService.result(operationId, null);
        Optional<SecurityCategoryResultVO.CategoryResult> categoryResult =
            vo.getCategoryResults().stream().filter(c -> "数据安全能力".equals(c.getCategoryName())).distinct()
                .findFirst();
        categoryResult.ifPresent(result -> {
            model.put("aqnlTotalScore", result.getCategoryTotalScore());
            model.put("aqnlScore", result.getCategoryScore());
            BigDecimal rate = calculateRate(result.getCategoryScore(), result.getCategoryTotalScore());
            model.put("aqnlRate", rate);
            model.put("aqnlLevel", convertAbilityLevel(rate.doubleValue()));
            detailContent(model, result.getContentResults());
        });

        Optional<SecurityCategoryResultVO.CategoryResult> czxwCategoryResult =
            vo.getCategoryResults().stream().filter(c -> "数据操作行为".equals(c.getCategoryName())).distinct()
                .findFirst();
        czxwCategoryResult.ifPresent(result -> {
            model.put("czxwTotalScore", result.getCategoryTotalScore());
            model.put("czxwScore", result.getCategoryScore());
            BigDecimal rate = calculateRate(result.getCategoryScore(), result.getCategoryTotalScore());
            model.put("czxwRate", rate);
            model.put("czxwLevel", convertActionLevel(rate.doubleValue()));
            detailContent(model, result.getContentResults());
        });

        Optional<SecurityCategoryResultVO.CategoryResult> aqglCategoryResult =
            vo.getCategoryResults().stream().filter(c -> "数据安全管理".equals(c.getCategoryName())).distinct()
                .findFirst();
        aqglCategoryResult.ifPresent(result -> {
            model.put("aqglTotalScore", result.getCategoryTotalScore());
            model.put("aqglScore", result.getCategoryScore());
            BigDecimal rate = calculateRate(result.getCategoryScore(), result.getCategoryTotalScore());
            model.put("aqglRate", rate);
            model.put("aqglLevel", convertSafetyAbilityLevel(rate.doubleValue()));
            detailContent(model, result.getContentResults());
        });

        Optional<SecurityCategoryResultVO.CategoryResult> aqzdCategoryResult =
            vo.getCategoryResults().stream().filter(c -> "数据安全制度".equals(c.getCategoryName())).distinct()
                .findFirst();
        aqzdCategoryResult.ifPresent(result -> {
            model.put("aqzdTotalScore", result.getCategoryTotalScore());
            model.put("aqzdScore", result.getCategoryScore());
            BigDecimal rate = calculateRate(result.getCategoryScore(), result.getCategoryTotalScore());
            model.put("aqzdRate", rate);
            model.put("aqzdLevel", convertAbilityLevel(rate.doubleValue()));
            detailContent(model, result.getContentResults());
        });
        model.put("score", vo.getScore());
        model.put("totalScore", vo.getScore());

    }

    private BigDecimal calculateRate(BigDecimal score, BigDecimal totalScore) {
        if (totalScore.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return score.divide(totalScore, 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
    }

    private Object convertActionLevel(double reachRate) {
        if (reachRate>= 0 && reachRate <= 30){
            return "较低";
        } else {
            return "较高";
        }
    }

    private void detailContent(Map<String, Object> model, List<SecurityCategoryResultVO.ContentResult> contentResults) {
        contentResults.forEach(contentResult -> {
            if ("分类分级".equals(contentResult.getContentName())){
                model.put("flfjScore",contentResult.getContentScore());
            } else if ("权限管控".equals(contentResult.getContentName())){
                model.put("qxgkScore",contentResult.getContentScore());
            } else if ("数据脱敏".equals(contentResult.getContentName())){
                model.put("sjtmScore",contentResult.getContentScore());
            } else if ("数据库审计".equals(contentResult.getContentName())){
                model.put("sjksjScore",contentResult.getContentScore());
            } else if ("数据加密".equals(contentResult.getContentName())){
                model.put("sjjmScore",contentResult.getContentScore());
            } else if ("数据水印".equals(contentResult.getContentName())){
                model.put("sjsyScore",contentResult.getContentScore());
            } else if ("态势感知".equals(contentResult.getContentName())){
                model.put("tsgzScore",contentResult.getContentScore());
            } else if ("个人敏感数据".equals(contentResult.getContentName())){
                model.put("grmgsjScore",contentResult.getContentScore());
            } else if ("违规删除数据行为".equals(contentResult.getContentName())){
                model.put("wgscsjScore",contentResult.getContentScore());
            } else if ("违规导出数据行为".equals(contentResult.getContentName())){
                model.put("wgdcsjScore",contentResult.getContentScore());
            } else if ("批量数据修改行为".equals(contentResult.getContentName())){
                model.put("plsjxgScore",contentResult.getContentScore());
            } else if ("批量查询数据行为".equals(contentResult.getContentName())){
                model.put("wgplcxsjScore",contentResult.getContentScore());
            } else if ("删库行为".equals(contentResult.getContentName())){
                model.put("skxwScore",contentResult.getContentScore());
            } else if ("用户权限".equals(contentResult.getContentName())){
                model.put("sjkzhqxScore",contentResult.getContentScore());
            } else if ("违规连接".equals(contentResult.getContentName())){
                model.put("sjkwgljScore",contentResult.getContentScore());
            } else if ("数据库版本补丁".equals(contentResult.getContentName())){
                model.put("sjkbbbdScore",contentResult.getContentScore());
            } else if ("连接超时机制".equals(contentResult.getContentName())){
                model.put("sjkljcsScore",contentResult.getContentScore());
            } else if ("默认端口".equals(contentResult.getContentName())){
                model.put("sjkmrdkScore",contentResult.getContentScore());
            } else if ("弱口令".equals(contentResult.getContentName())){
                model.put("sjkrmlScore",contentResult.getContentScore());
            } else if ("用户配置".equals(contentResult.getContentName())){
                model.put("sjkyhmmScore",contentResult.getContentScore());
            } else if ("文件安全".equals(contentResult.getContentName())){
                model.put("sjkwjaqScore",contentResult.getContentScore());
            } else if ("物理备份".equals(contentResult.getContentName())){
                model.put("wlkbfScore",contentResult.getContentScore());
            } else if ("连接数限制".equals(contentResult.getContentName())){
                model.put("ljsxzScore",contentResult.getContentScore());
            } else if ("网络传输加密".equals(contentResult.getContentName())){
                model.put("wlcsjmScore",contentResult.getContentScore());
            } else if ("数据库归档情况".equals(contentResult.getContentName())){
                model.put("sjkgdScore",contentResult.getContentScore());
            } else if ("安全组织机构".equals(contentResult.getContentName())){
                model.put("aqzujgScore",contentResult.getContentScore());
            } else if ("分类分级管理".equals(contentResult.getContentName())){
                model.put("flfjglScore",contentResult.getContentScore());
            } else if ("人员安全管理".equals(contentResult.getContentName())){
                model.put("ryaqglScore",contentResult.getContentScore());
            } else if ("合作外包管理".equals(contentResult.getContentName())){
                model.put("hzwbglScore",contentResult.getContentScore());
            } else if ("开发运维管理".equals(contentResult.getContentName())){
                model.put("kfywglScore",contentResult.getContentScore());
            }
        });
    }

    private String convertSafetyAbilityLevel(double rate) {
        if (rate>= 0 && rate < 40){
            return "较高";
        } else if (rate>= 40 && rate < 70){
            return "中等";
        } else {
            return "较低";
        }
    }

    private String convertAbilityLevel(double rate) {
        if (rate>= 0 && rate < 30){
            return "较高";
        } else if (rate>= 30 && rate < 60){
            return "中等";
        } else {
            return "较低";
        }
    }

    private void compareAnalysis(Map<String, Object> model, List<Integer> serviceContentList, String operationId) {
        try {
            model.put("ep", false);
            if (serviceContentList.contains(AbilityType.ENCRYPT.getCode())) {
                List<ApiCompareVO> apiCompareList =
                    iAppService.compareReportQuery(operationId, AbilityType.ENCRYPT.getCode());
                model.put("ep", true);
                List<Map<String, Object>> epDiffList = new ArrayList<>();

                Map<String, AtomicInteger> systemReachMap = new HashMap<>(16);
                apiCompareList.forEach(apiCompareVO -> {
                    apiCompareVO.setResult(convertResult(apiCompareVO.getResult(), AbilityType.ENCRYPT));
                    if (systemReachMap.containsKey(apiCompareVO.getBusSystem())) {
                        AtomicInteger count = systemReachMap.get(apiCompareVO.getBusSystem());
                        if (!"一致".equals(apiCompareVO.getResult())) {
                            count.incrementAndGet();
                        }
                        systemReachMap.put(apiCompareVO.getBusSystem(), count);
                    } else {
                        AtomicInteger total = new AtomicInteger(0);
                        if (!"一致".equals(apiCompareVO.getResult())) {
                            total.incrementAndGet();
                        }
                        systemReachMap.put(apiCompareVO.getBusSystem(), total);
                    }
                    AtomicInteger sort = new AtomicInteger(1);
                    apiCompareVO.getTableDetailList()
                        .forEach(tableDetail -> tableDetail.getSampleDetailList().forEach(sampleDetail -> {
                            Map<String, Object> diffMap = new HashMap<>(16);
                            diffMap.put("sort", sort.getAndIncrement());
                            diffMap.put("busSystem", apiCompareVO.getBusSystem());
                            diffMap.put("dbName", apiCompareVO.getDbName());
                            diffMap.put("sourceTable", tableDetail.getSourceTable());
                            diffMap.put("sourceColumn", sampleDetail.getSourceColumn());
                            diffMap.put("targetTable", tableDetail.getTargetTable());
                            diffMap.put("targetColumn", sampleDetail.getTargetColumn());
                            diffMap.put("result", sampleDetail.getResult());
                            epDiffList.add(diffMap);
                        }));
                });
                int reachCount = 0;
                for (Map.Entry<String, AtomicInteger> entry : systemReachMap.entrySet()) {
                    if (entry.getValue().get() > 0) {
                        reachCount++;
                    }
                }
                model.put("epList", apiCompareList);
                model.put("epDiffList", epDiffList);
                model.put("epTotal", reachCount);
                double epReachRate = BigDecimal.valueOf(reachCount)
                    .divide(BigDecimal.valueOf(systemReachMap.size()), 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100)).doubleValue();
                model.put("epReachRate", epReachRate);
                model.put("epLevel", convertLevel(epReachRate));
            }

            model.put("dm", false);
            if (serviceContentList.contains(AbilityType.DESENSITIZATION.getCode())) {
                List<ApiCompareVO> apiCompareList =
                    iAppService.compareReportQuery(operationId, AbilityType.DESENSITIZATION.getCode());
                model.put("dm", true);
                List<Map<String, Object>> dmDiffList = new ArrayList<>();
                Map<String, AtomicInteger> systemReachMap = new HashMap<>(16);
                apiCompareList.forEach(apiCompareVO -> {
                    apiCompareVO.setResult(convertResult(apiCompareVO.getResult(), AbilityType.DESENSITIZATION));
                    if (systemReachMap.containsKey(apiCompareVO.getBusSystem())) {
                        AtomicInteger count = systemReachMap.get(apiCompareVO.getBusSystem());
                        if (!"一致".equals(apiCompareVO.getResult())) {
                            count.incrementAndGet();
                        }
                        systemReachMap.put(apiCompareVO.getBusSystem(), count);
                    } else {
                        AtomicInteger total = new AtomicInteger(0);
                        if (!"一致".equals(apiCompareVO.getResult())) {
                            total.incrementAndGet();
                        }
                        systemReachMap.put(apiCompareVO.getBusSystem(), total);
                    }
                    AtomicInteger sort = new AtomicInteger(1);
                    apiCompareVO.getTableDetailList()
                        .forEach(tableDetail -> tableDetail.getSampleDetailList().forEach(sampleDetail -> {
                            Map<String, Object> diffMap = new HashMap<>(16);
                            diffMap.put("sort", sort.getAndIncrement());
                            diffMap.put("busSystem", apiCompareVO.getBusSystem());
                            diffMap.put("dbName", apiCompareVO.getDbName());
                            diffMap.put("sourceTable", tableDetail.getSourceTable());
                            diffMap.put("sourceColumn", sampleDetail.getSourceColumn());
                            diffMap.put("targetTable", tableDetail.getTargetTable());
                            diffMap.put("targetColumn", sampleDetail.getTargetColumn());
                            diffMap.put("result", sampleDetail.getResult());
                            dmDiffList.add(diffMap);
                        }));
                });
                int reachCount = 0;
                for (Map.Entry<String, AtomicInteger> entry : systemReachMap.entrySet()) {
                    if (entry.getValue().get() > 0) {
                        reachCount++;
                    }
                }
                model.put("dmList", apiCompareList);
                model.put("dmDiffList", dmDiffList);
                model.put("dmTotal", reachCount);
                double dmReachRate = BigDecimal.valueOf(reachCount)
                    .divide(BigDecimal.valueOf(systemReachMap.size()), 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100)).doubleValue();
                model.put("dmReachRate", dmReachRate);
                model.put("dmLevel", convertLevel(dmReachRate));
            }
        } catch (Exception e){
            log.error("", e);
        }
    }


    private String convertResult(String result, AbilityType type) {
        if ("一致".equals(result)){
            return AbilityType.DESENSITIZATION == type ? "未实现数据脱敏" : "未实现数据加密";
        } else if ("部分一致".equals(result)){
            return  AbilityType.DESENSITIZATION == type ? "部分实现数据脱敏" : "部分实现数据加密";
        } else {
            return  AbilityType.DESENSITIZATION == type ? "完全实现数据脱敏" : "完全实现数据加密";
        }
    }

    private String convertLevel(double epReachRate) {
        if (epReachRate>= 0 && epReachRate <= 70){
            return "较高";
        } else {
            return "较低";
        }
    }

    private void dataSafetyAbilityDetails(List<SecurityLabelItemReportVO> resultList, Map<String, Object> model,
        List<ExportWordChart> chartList, SecurityOperation securityOperation, List<Integer> serviceContentList) {
        Integer operationId = securityOperation.getId();
        resultList.stream().collect(Collectors.groupingBy(SecurityLabelItemReportVO::getContent)).forEach((k,v)->{
            List<SecurityLabelItemReportVO> list = v.stream().filter(SecurityLabelItemReportVO::getChecked).collect(Collectors.toList());
            Optional<SecurityLabelItemReportVO> optional  = list.stream().findFirst();
            String remark = "";
            if (optional.isPresent()){
                remark = optional.get().getRemark();
            }
            if ("分类分级".equals(k)){
                model.put("flfjRemark", remark);
                model.put("flfjResult", getResult(list, v.size()));
            } else if ("权限管控".equals(k)) {
                model.put("qxgkRemark", remark);
                model.put("qxgkResult", getResult(list, v.size()));
            } else if ("数据库审计".equals(k)) {
                model.put("sjksjRemark", remark);
                model.put("sjksjResult", getResult(list, v.size()));
            } else if ("数据水印".equals(k)) {
                model.put("sjsyRemark", remark);
                model.put("sjsyResult", getResult(list, v.size()));
            }  else if ("数据脱敏".equals(k)) {
                model.put("sjtmRemark", remark);
                model.put("sjtmResult", getResult(list, v.size()));
            } else if ("数据加密".equals(k)) {
                model.put("sjjmRemark", remark);
                model.put("sjjmResult", getResult(list, v.size()));
            } else if ("态势感知".equals(k)) {
                model.put("tsgzRemark", remark);
                model.put("tsgzResult", getResult(list, v.size()));
            } else if ("安全组织机构".equals(k)) {
                model.put("aqzzjgRemark", remark);
                model.put("aqzzjgResult", getResult(list, v.size()));
            } else if ("分类分级管理".equals(k)) {
                model.put("flfjglRemark", remark);
                model.put("flfjglResult", getResult(list, v.size()));
            } else if ("人员安全管理".equals(k)) {
                model.put("ryaqglRemark", remark);
                model.put("ryaqglResult", getResult(list, v.size()));
            } else if ("合作外包管理".equals(k)) {
                model.put("hzwbglRemark", remark);
                model.put("hzwbglResult", getResult(list, v.size()));
            } else if ("开发运维管理".equals(k)) {
                model.put("kfywglRemark", remark);
                model.put("kfywglResult", getResult(list, v.size()));
            }  else if ("个人敏感数据".equals(k)) {
                model.put("grmgDesc", remark);
                model.put("grmsResult", getResult(list, v.size()));
            } else if ("违规删除数据行为".equals(k)) {
                model.put("wgscDesc", remark);
                model.put("wgscResult", getResult(list, v.size()));
            } else if ("违规导出数据行为".equals(k)) {
                model.put("wgdcDesc", remark);
                model.put("wgdcResult", getResult(list, v.size()));
            } else if ("批量数据修改行为".equals(k)) {
                model.put("plxgDesc", remark);
                model.put("plxgResult", getResult(list, v.size()));
            } else if ("批量查询数据行为".equals(k)) {
                model.put("wgplcxDesc", remark);
                model.put("wgplcxResult", getResult(list, v.size()));
            } else if ("删库行为".equals(k)) {
                model.put("skxwDesc", remark);
                model.put("skxwResult", getResult(list, v.size()));
            }
        });

        //3.2 权限管控
        //3.2.2 高危数据权限分析
        RequestModel<OperationIdDto> requestModel = new RequestModel<>();
        OperationIdDto operationIdDto = new OperationIdDto();
        operationIdDto.setOperationId(String.valueOf(operationId));
        requestModel.setPrivator(operationIdDto);

        //查询资产盘点表
        QueryWrapper<CoInventory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", String.valueOf(operationId));
        queryWrapper.orderByDesc("bus_system");
        List<CoInventory> coInventoryList = coInventoryMapper.selectList(queryWrapper);
        List<String> coInventories =
            coInventoryList.stream().map(CoInventory::getBusSystem).distinct().collect(Collectors.toList());

        assetAnalysis(chartList, requestModel, model, coInventories, coInventoryList, serviceContentList);


    }

    private String getResult(List<SecurityLabelItemReportVO> list, int size) {
        return list.isEmpty() ? "不符合" : (list.size() < size-1 ? "部分符合" : "符合");
    }

    /**
     * 3.资产分析
     *
     * @param chartList
     * @param requestModel
     * @param model
     * @param coInventories
     * @param coInventoryList
     * @param serviceContentList
     */
    protected void assetAnalysis(List<ExportWordChart> chartList, RequestModel<OperationIdDto> requestModel,
        Map<String, Object> model, List<String> coInventories, List<CoInventory> coInventoryList,
        List<Integer> serviceContentList) {
        CoOperationMapper coOperationMapper = SpringUtils.getBean(CoOperationMapper.class);
        CoConstantMapper coConstantMapper = SpringUtils.getBean(CoConstantMapper.class);
        CoAuthorityService coAuthorityService = SpringUtils.getBean(CoAuthorityService.class);

        // 资产盘点
        model.put("assetsCheck", false);
        if (serviceContentList.contains(SecurityContentType.SENSITIVE.getCode())) {
            model.put("assetsCheck", true);
            model.put("relatedSystem", coInventories.isEmpty() ? null : String.join("、", coInventories));
            model.put("relatedSystemNum", coInventories.size());
            //数据资产总条数
            int dataAssetsTotal = coInventoryList.size();
            //高敏感资产
            List<CoInventory> highSensitiveAssetsList = new ArrayList<>();
            QueryWrapper<CoConstant> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("customer_id",
                coOperationMapper.selectCustomIdByOperationId(requestModel.getPrivator().getOperationId()));
            List<CoConstant> coConstants = coConstantMapper.selectList(queryWrapper);
            //客户最高敏感等级
            int level = coConstants.get(0).getHighestSensitiveLevel();
            //客户最高敏感等级
            sensitiveAssetProcess(level, coInventoryList, highSensitiveAssetsList, new ArrayList<>(),
                new ArrayList<>());

            //高敏感资产占比
            double highSensitiveAssetsProportion = dataAssetsTotal == 0 ? 0.0 :
                Arith.round((double)highSensitiveAssetsList.size() / dataAssetsTotal * 100, 2);
            model.put("dataAssetsNum", dataAssetsTotal);
            model.put("highSensitiveAssetsNum", highSensitiveAssetsList.size());
            model.put("highSensitiveAssetsProportion", highSensitiveAssetsProportion);

            List<ExportWordChart> sensitiveChartList =
                chartList.stream().filter(s -> "sensitiveAssetsProportionImg".equals(s.getName())).collect(Collectors.toList());
            Map<String, String> imageBase64Data1 = getPicture(sensitiveChartList);
            putImage("sensitiveAssetsProportionImg", imageBase64Data1, model);

        }

        // 数据权限
        model.put("dataAuthority", false);
        if (serviceContentList.contains(SecurityContentType.SENSITIVE.getCode())) {
            model.put("dataAuthority", true);
            List<ReportAuthorityStatusQuoVo> statusQuoList = coAuthorityService.queryStatusQuo(requestModel);
            int crudTableNum = 0;
            int insertTableNum = 0;
            int deleteTableNum = 0;
            int dropTableNum = 0;
            int updateTableNum = 0;
            int selectTableNum = 0;
            int usernameTotal = 0;
            for (ReportAuthorityStatusQuoVo s : statusQuoList) {
                if (Objects.equals(s.getName(), ReportAuthorityPriEnum.CRUD_TABLE.getInfo())) {
                    crudTableNum = s.getUsernameNum();
                } else if (Objects.equals(s.getName(), ReportAuthorityPriEnum.INSERT_TABLE.getInfo())) {
                    insertTableNum = s.getUsernameNum();
                } else if (Objects.equals(s.getName(), ReportAuthorityPriEnum.DELETE_TABLE.getInfo())) {
                    deleteTableNum = s.getUsernameNum();
                } else if (Objects.equals(s.getName(), ReportAuthorityPriEnum.DROP_TABLE.getInfo())) {
                    dropTableNum = s.getUsernameNum();
                } else if (Objects.equals(s.getName(), ReportAuthorityPriEnum.UPDATE_TABLE.getInfo())) {
                    updateTableNum = s.getUsernameNum();
                } else if (Objects.equals(s.getName(), ReportAuthorityPriEnum.SELECT_TABLE.getInfo())) {
                    selectTableNum = s.getUsernameNum();
                }
                usernameTotal = s.getTotal();
            }

            double crudTableNumProportion = usernameTotal > 0 ? (double)crudTableNum / (double)usernameTotal : 0;
            double dropTableNumProportion = usernameTotal > 0 ? (double)dropTableNum / (double)usernameTotal : 0;
            double deleteTableNumProportion = usernameTotal > 0 ? (double)deleteTableNum / (double)usernameTotal : 0;
            double updateTableNumProportion = usernameTotal > 0 ? (double)updateTableNum / (double)usernameTotal : 0;
            double insertTableNumProportion = usernameTotal > 0 ? (double)insertTableNum / (double)usernameTotal : 0;
            double selectTableNumProportion = usernameTotal > 0 ? (double)selectTableNum / (double)usernameTotal : 0;
            model.put("usernameTotal", usernameTotal);
            model.put("crudTableNum", crudTableNum);
            model.put("dropTableNum", dropTableNum);
            model.put("deleteTableNum", deleteTableNum);
            model.put("updateTableNum", updateTableNum);
            model.put("insertTableNum", insertTableNum);
            model.put("selectTableNum", selectTableNum);
            model.put("crudTableNumProportion", Arith.round(crudTableNumProportion * 100, 2));
            model.put("dropTableNumProportion", Arith.round(dropTableNumProportion * 100, 2));
            model.put("deleteTableNumProportion", Arith.round(deleteTableNumProportion * 100, 2));
            model.put("updateTableNumProportion", Arith.round(updateTableNumProportion * 100, 2));
            model.put("insertTableNumProportion", Arith.round(insertTableNumProportion * 100, 2));
            model.put("selectTableNumProportion", Arith.round(selectTableNumProportion * 100, 2));

            // 用户权限分类统计结果
            // 生命周期图表

            //3.2.4	用户-资产权限分布分析
            List<ReportAuthorityUserAssetVo> reportAuthorityUserAssetVos =
                coAuthorityService.queryUserAssetsResultWithBusSystem(requestModel);
            AtomicInteger i = new AtomicInteger();
            model.put("totalUser", reportAuthorityUserAssetVos.size());
            List<ReportAuthorityUserAssetVo> top20 = reportAuthorityUserAssetVos.stream().peek(p -> p.setSort(i.getAndIncrement() + 1)).limit(20).collect(Collectors.toList());
            model.put("userAssetAuthorityList", top20);

            // 3.2.2 高危数据权限分布
            List<BusSystemAuthorityOverviewDTO> busSystemAuthorityOverviewList =
                coAuthorityService.queryBusSystemAuthorityOverview(requestModel.getPrivator().getOperationId());
            AtomicInteger sort = new AtomicInteger();
            busSystemAuthorityOverviewList.forEach(p -> p.setSort(sort.getAndIncrement() + 1));
            model.put("busSystemAuthorityList", busSystemAuthorityOverviewList);

        }
    }

    protected void sensitiveAssetProcess(int level, List<CoInventory> coInventories,
        List<CoInventory> highSensitiveAssetsList, List<CoInventory> mediumSensitiveAssetsList,
        List<CoInventory> lowSensitiveAssetsList) {
        // 设计一个哈希表存储level对应的敏感等级分类标准
        Map<Integer, int[]> sensitivityMapping = new HashMap<>();
        sensitivityMapping.put(3, new int[] {1, 2});
        sensitivityMapping.put(4, new int[] {1, 2});
        sensitivityMapping.put(5, new int[] {1, 3});
        sensitivityMapping.put(6, new int[] {2, 4});
        sensitivityMapping.put(7, new int[] {2, 5});
        sensitivityMapping.put(8, new int[] {2, 5});

        int[] sensitivityThresholds = sensitivityMapping.get(level);
        for (CoInventory coInventory : coInventories) {
            Integer sensitiveLevel = coInventory.getSensitiveLevel();
            if (sensitiveLevel != null) {
                if (sensitiveLevel <= sensitivityThresholds[0]) {
                    lowSensitiveAssetsList.add(coInventory);
                } else if (sensitiveLevel <= sensitivityThresholds[1]) {
                    mediumSensitiveAssetsList.add(coInventory);
                } else {
                    highSensitiveAssetsList.add(coInventory);
                }
            }
        }
    }

    private void baseAssessment(Map<String, Object> model, List<ExportWordChart> chartList,
        List<Integer> serviceContentList, String operationId, CoProject coProject) {
        DetectionResultMapper detectionResultMapper = SpringUtils.getBean(DetectionResultMapper.class);
        ScanTaskService scanTaskService = SpringUtils.getBean(ScanTaskService.class);
        SecurityOperation securityOperation = securityOperationMapper.selectById(Integer.parseInt(operationId));

        // 5.1基础评估
        model.put("baseAssessment", false);
        if (serviceContentList.contains(SecurityContentType.BASIC.getCode())) {
            model.put("baseAssessment", true);

            List<DetectionResult> detectionResults = detectionResultMapper
                .selectList(new QueryWrapper<DetectionResult>().eq("operation_id", operationId));
            Set<String> dbTypeSet = new HashSet<>();
            Map<String, List<DetectionResult>> dbConfigMap = new HashMap<>(16);
            int successCount = 0;
            int failCount = 0;
            for (DetectionResult detectionResult : detectionResults) {
                dbTypeSet.add(DataSourceType.getType(detectionResult.getDbType()).getName());
                if (dbConfigMap.containsKey(detectionResult.getDbConfig())) {
                    dbConfigMap.get(detectionResult.getDbConfig()).add(detectionResult);
                } else {
                    List<DetectionResult> list = new ArrayList<>();
                    list.add(detectionResult);
                    dbConfigMap.put(detectionResult.getDbConfig(), list);
                }
                if (detectionResult.getAccord()) {
                    successCount++;
                } else {
                    failCount++;
                }
            }

            model.put("dbTypeStr", String.join("、", dbTypeSet));
            model.put("dbCount", dbConfigMap.size());
            model.put("totalCount", detectionResults.size());
            model.put("successCount", successCount);
            model.put("failCount", failCount);
            BigDecimal rate = BigDecimal.valueOf(successCount)
                .divide(BigDecimal.valueOf(detectionResults.size()), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
            model.put("successRate", rate.setScale(2, RoundingMode.HALF_UP));

            //整体检测记录
            //1、2阶段查询
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            List<Map<String, Object>> detectionRecordList = new ArrayList<>();
            List<String> projectManagerList = StrUtil.split(coProject.getProjectManager(), StrUtil.C_COMMA);
            List<String> executorList = StrUtil.split(securityOperation.getExecutorAccount(), StrUtil.C_COMMA);
            model.put("executor",
                String.join("、", CollUtil.unionDistinct(projectManagerList, executorList)));
            model.put("createTime", simpleDateFormat.format(securityOperation.getCreateTime()));

            // 数据库检测详情
            List<Map<String, Object>> dbSourceList = new ArrayList<>();

            AtomicInteger sort = new AtomicInteger();
            dbConfigMap.forEach((k, v) -> {

                Map<String, Object> recordMap = new HashMap<>(16);
                DetectionResult detectionResult = v.get(0);

                SourceConfig sourceConfig =
                    com.alibaba.fastjson.JSON.parseObject(detectionResult.getDbConfig(), SourceConfig.class);
                String dbType = DataSourceType.getType(v.get(0).getDbType()).getName();
                long count = v.stream().filter(DetectionResult::getAccord).count();
                int total = v.size();
                recordMap.put("sort", sort.incrementAndGet());
                recordMap.put("dbName", sourceConfig == null ? null : sourceConfig.getConfigName());
                recordMap.put("dbType", dbType);
                recordMap.put("pointCount", total);
                BigDecimal pointRate =
                    BigDecimal.valueOf(count).divide(BigDecimal.valueOf(total), 4, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(100));
                recordMap.put("pointRate", pointRate.setScale(2, RoundingMode.HALF_UP));
                detectionRecordList.add(recordMap);

                Map<String, Object> dbSourceInfo = new HashMap<>(16);
                dbSourceInfo.put("dbSourceName", sourceConfig == null ? null : sourceConfig.getConfigName());
                List<ExportWordChart> chartList2 = chartList.stream()
                    .filter(s -> String.format("基础评估分析-%s数据源检测结果图", sourceConfig.getConfigName()).equals(s.getName()))
                    .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(chartList2)) {
                    dbSourceInfo.put("dbSourceAccordImg", getPictureStream(getPicture(chartList2)));
                }
                dbSourceInfo.put("dbType", DataSourceType.getType(detectionResult.getDbType()).getName());
                dbSourceInfo.put("ip", sourceConfig == null ? null : sourceConfig.getHost());
                dbSourceInfo.put("port", sourceConfig == null ? null : sourceConfig.getPort());
                dbSourceInfo.put("dbName", sourceConfig == null ? null : sourceConfig.getDbName());
                dbSourceInfo.put("total", total);
                dbSourceInfo.put("sCount", count);
                dbSourceInfo.put("fCount", total - count);
                dbSourceInfo.put("rate", recordMap.get("pointRate"));

                // 检查项详情
                List<Map<String, Object>> detectionDetails = new ArrayList<>();
                Map<String, List<DetectionResult>> optionMap =
                    v.stream().filter(result -> StrUtil.isNotEmpty(result.getOption()))
                        .collect(Collectors.groupingBy(DetectionResult::getOption));
                optionMap.forEach((optionName, pointList)->{
                    Map<String, Object> optionInfo = new HashMap<>(16);
                    optionInfo.put("detectionName", optionName);
                    // 检查点详情
                    List<Map<String, Object>> detectionPointDetails = new ArrayList<>();
                    pointList.forEach(point -> {
                        Map<String, Object> pointInfo = new HashMap<>(16);
                        pointInfo.put("detectionPoint", point.getContent());
                        pointInfo.put("detectionResult", point.getAccord() ? "符合" : "不符合");
                        pointInfo.put("dbType", DataSourceType.getType(point.getDbType()).getName());
                        pointInfo.put("desc", point.getDescribe());
                        if (StrUtil.isNotEmpty(point.getResult())) {
                            JSONObject jsonObject = com.alibaba.fastjson.JSON.parseObject(point.getResult());
                            JSONArray jsonArray = jsonObject.getJSONArray("data");
                            StringBuilder sb = new StringBuilder();
                            jsonArray.forEach(o -> {
                                ((JSONObject)o)
                                    .forEach((key, value) -> sb.append(key).append(":").append(value).append("\n"));
                            });
                            pointInfo.put("result", sb.length() > 0 ? sb.deleteCharAt(sb.lastIndexOf("\n")): sb);
                        }
                        // 不符合的情况下需展示建议措施
                        if (Boolean.FALSE.equals(point.getAccord())) {
                            pointInfo.put("suggest", point.getUnqualified());
                        }
                        detectionPointDetails.add(pointInfo);
                    });
                    optionInfo.put("detectionPointDetails", detectionPointDetails);
                    detectionDetails.add(optionInfo);
                });
                dbSourceInfo.put("detectionDetails", detectionDetails);
                dbSourceList.add(dbSourceInfo);
            });
            model.put("detectionRecordList", detectionRecordList);
            model.put("dbSourceList", dbSourceList);

            List<ExportWordChart> chartList1 =
                chartList.stream().filter(s -> "基础评估分析-数据库安全基线达标率分布图".equals(s.getName())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(chartList1)) {
                model.put("dbAccordRateImg", getPictureStream(getPicture(chartList1)));
            }

            model.put("scanTaskExist", false);
            List<ScanTaskVO> scanTaskList = scanTaskService.queryScanTask(operationId);
            if (CollUtil.isNotEmpty(scanTaskList)){
                int pocNum = 0;
                int highNum = 0;
                int midNum = 0;
                int lowNum = 0;
                int pwNum = 0;
                List<Map<String, Object>> scanRiskList = new ArrayList<>();
                Map<String, Object> hostRisk = new HashMap<>(16);
                int hostSort = 1;
                for (ScanTaskVO scanTaskVO : scanTaskList){
                    hostRisk.put("sort", hostSort++);
                    ScanTaskInfoVO scanTaskInfoVO = scanTaskService.queryScanTaskDetail(scanTaskVO.getTaskId());
                    for (ScanTaskInfoVO.Task task : scanTaskInfoVO.getTask()){
                        pocNum += task.getPocRisk();
                        highNum += task.getHighRisk();
                        midNum += task.getMiddleRisk();
                        lowNum += task.getLowRisk();
                        pwNum += task.getPwNum();
                        hostRisk.put("host", task.getIp());
                        hostRisk.put("riskNum", String
                            .format("可入侵漏洞%d个,高风险漏洞%d个,中风险漏洞%d个,低风险漏洞%d个,弱口令%d个", task.getPocRisk(), task.getHighRisk(),
                                task.getMiddleRisk(), task.getLowRisk(), task.getPwNum()));
                    }
                    scanRiskList.add(hostRisk);
                }
                model.put("scanTaskExist", true);
                model.put("scanRiskList", scanRiskList);
                model.put("hostNum", scanTaskList.size());
                model.put("pocNum", pocNum);
                model.put("highNum", highNum);
                model.put("midNum", midNum);
                model.put("lowNum", lowNum);
                model.put("pwNum", pwNum);
            }
        }
    }
}
