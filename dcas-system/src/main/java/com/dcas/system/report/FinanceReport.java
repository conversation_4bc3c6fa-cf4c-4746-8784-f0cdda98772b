package com.dcas.system.report;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.NumberChineseFormatter;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.stream.StreamUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.domain.entity.*;
import com.dcas.common.enums.*;
import com.dcas.common.mapper.*;
import com.dcas.common.model.dto.*;
import com.dcas.common.model.excel.InventoryAttachmentJrExcel;
import com.dcas.common.model.vo.*;
import com.dcas.system.report.attachment.AttachmentReportFactory;
import com.dcas.system.service.IRiskAnalysisService;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.deepoove.poi.plugin.toc.TOCRenderPolicy;
import com.deepoove.poi.util.PoitlIOUtils;
import com.deepoove.poi.xwpf.NiceXWPFDocument;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.*;
import java.math.BigDecimal;
import java.text.Collator;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 金融报告
 *
 * <AUTHOR>
 * @date 2024/01/08 16:00
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class FinanceReport extends AbstractReport implements ReportInterface {

    private final ModelFileMapper modelFileMapper;
    private final CoInventoryMapper coInventoryMapper;
    private final CoVerificationMapper coVerificationMapper;
    private final StandardItemMapper standardItemMapper;
    private final CoGapAnalysisMapper coGapAnalysisMapper;
    private final AdviseRiskMapper adviseRiskMapper;
    private final CoLegalMapper coLegalMapper;
    private final DynamicProcessTreeMapper dynamicProcessTreeMapper;
    private final IRiskAnalysisService iRiskAnalysisService;
    private final CoModelAnalysisResultMapper coModelAnalysisResultMapper;
    private final LawItemMapper lawItemMapper;
    private final TagMapper tagMapper;

    @Value("${safety.profile}")
    private String basePath;

    @Override
    public String exportWord(ExportWordDto dto, QueryProjectOperationExportVo poVo, Long modelId) throws Exception {
        List<String> filePathList = new ArrayList<>();
        CompletableFuture<String> future = CompletableFuture.allOf(CompletableFuture.supplyAsync(() -> {
                try {
                    dto.setNeedExcludeContent(getNeedExcludeContent());
                    return process(dto, poVo, modelId);
                } catch (IOException e) {
                    log.error("导出报告失败", e);
                    return null;
                }
            }).whenComplete((v, th) -> {
                if (th != null) {
                    log.error("", th);
                }
                if (v != null) {
                    log.info(v);
                    filePathList.add(v);
                }
            }), CompletableFuture.supplyAsync(() -> {
                try {
                    return AttachmentReportFactory.getAssetReportHandler(ReportTypeEnum.FINANCE).exportWord(dto, poVo);
                } catch (Exception e) {
                    log.error("导出报告失败", e);
                    return null;
                }
            }).whenComplete((v, th) -> {
                if (th != null) {
                    log.error("", th);
                }
                if (v != null) {
                    log.info(v);
                    filePathList.add(v);
                }
            }), CompletableFuture.supplyAsync(() -> {
                try {
                    return ReportFactory.getReportHandler(ReportTypeEnum.TEC_DETECTION).exportWord(dto, poVo);
                } catch (Exception e) {
                    log.error("导出技术检测报告失败", e);
                    return null;
                }
            }).whenComplete((v, th) -> {
                if (th != null) {
                    log.error("", th);
                }
                if (v != null) {
                    log.info(v);
                    filePathList.add(v);
                }
            })).thenRun(() -> addScanReportToPath(filePathList, dto.getOperationId()))
            .thenApply(v -> zip(filePathList, poVo.getOperationName(), basePath));
        return future.get();
    }

    @Override
    @SchemaSwitch(ExportWordDto.class)
    public String process(ExportWordDto dto, QueryProjectOperationExportVo poVo, Long modelId) throws IOException {

        ModelFile modelFile = modelFileMapper.selectOne(new QueryWrapper<ModelFile>().eq("model_id", modelId));
        if (modelFile == null) {
            String msg = String.format("找不到模型ID=%d对应的文件", modelId);
            throw new IOException(msg);
        }
        InputStream inputStream = new FileInputStream(modelFile.getFilePath());

        //数据模型
        Map<String, Object> model = new HashMap<>(16);

        //准备数据
        String operationId = dto.getOperationId();
        List<ExportWordChart> chartList = dto.getChartList();
        List<ExportWordChart> analysisChartList =
            chartList.stream().filter(s -> s.getName().contains("图") || s.getName().contains("表格"))
                .collect(Collectors.toList());

        // 报告公共部分
        putCommonInfo(poVo, model);

        //查询资产盘点表
        QueryWrapper<CoInventory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", dto.getOperationId());
        queryWrapper.orderByDesc("bus_system");
        List<CoInventory> coInventoryList = coInventoryMapper.selectList(queryWrapper);
        List<String> coInventories =
            coInventoryList.stream().map(CoInventory::getBusSystem).distinct().collect(Collectors.toList());

        // 图片赋值
        putModelPicture(chartList, model);

        RequestModel<OperationIdDto> requestModel = new RequestModel<>();
        OperationIdDto operationIdDto = new OperationIdDto();
        operationIdDto.setOperationId(operationId);
        requestModel.setPrivator(operationIdDto);

        //评估内容
        List<Long> serviceContentList = JSON.parseArray("[" + poVo.getServiceContent() + "]", Long.class);
        //3.资产梳理
        //3.2.2 高危数据权限分析
        model.put("assetAnalysis", false);
        if (serviceContentList.contains(LabelEnum.ZCSL.getCode())) {
            model.put("assetAnalysis", true);
            assetAnalysis(chartList, requestModel, model, coInventories, coInventoryList, serviceContentList);
        }

        //4.安全现状
        //4.1.1	评估概述
        model.put("currentSituationAnalysis", false);
        if (serviceContentList.contains(LabelEnum.AQXZ.getCode())) {
            model.put("currentSituationAnalysis", true);
            currentSituationAnalysis(operationId, model, analysisChartList);
        }

        //5.安全评估
        model.put("riskAssessment", false);
        if (serviceContentList.contains(LabelEnum.FXPG.getCode())) {
            model.put("riskAssessment", true);
            riskAssessment(model, dto, operationId, chartList, serviceContentList);
        }

        // 6 处置建议
        // 6.1合规风险
        model.put("processSuggestions", false);
        if (serviceContentList.contains(LabelEnum.CZJY.getCode())) {
            model.put("processSuggestions", true);
            processSuggestions(operationId, model);
        }
        //配置模板
        LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
        TOCRenderPolicy tocPolicy = new TOCRenderPolicy();
        Configure config = Configure.builder().bind("dataSaftyHighSuggests", policy).bind("dataTecHighSuggests", policy)
            .bind("dataSaftyMidSuggests", policy).bind("dataTecMidSuggests", policy).bind("tocContents", tocPolicy)
            .bind("busSystemAuthorityList", policy).bind("userAssetAuthorityList", policy)
            .bind("lifecycleTabList", policy).bind("dataSafetyTabList", policy).bind("maintenanceTabList", policy)
            .bind("detectionRecordList", policy).bind("detectionPointDetails", policy).bind("scanRiskList", policy)
            .bind("legalList", policy).bind("lifecycleRiskList", policy).bind("dataSafetyList", policy)
            .bind("maintenanceList", policy).bind("riskList", policy).build();

        //加载模板渲染数据
        XWPFTemplate template = XWPFTemplate.compile(inputStream, config).render(model);
        NiceXWPFDocument xwpfDocument = template.getXWPFDocument();
        xwpfDocument.enforceUpdateFields();

        String realFileName = String.format("%s报告-金融专用.docx", poVo.getOperationName());
        String path = String.join(File.separator, basePath, "temp", realFileName);
        FileUtil.touch(path);

        //输出结果
        OutputStream out = new FileOutputStream(path);
        BufferedOutputStream bos = new BufferedOutputStream(out);
        xwpfDocument.write(bos);
        bos.flush();
        out.flush();
        PoitlIOUtils.closeQuietlyMulti(template, xwpfDocument, bos, out);
        return path;
    }

    private void riskAssessment(Map<String, Object> model, ExportWordDto privator,
        String operationId, List<ExportWordChart> chartList, List<Long> serviceContentList) {
        List<Map<String, Object>> fileRefList = Lists.newArrayList();
        AtomicInteger fileIndex = new AtomicInteger(1);

        // 5.1基础评估
        baseAssessment(model, chartList, serviceContentList, privator);

        // 5.2合规评估
        lawAssessment(model, privator, serviceContentList, operationId, fileRefList, fileIndex);

        // 5.3风险评估
        riskContentsAssessment(operationId, model, serviceContentList,  privator);
    }

    private void riskContentsAssessment(String operationId, Map<String, Object> model, List<Long> serviceContentList, ExportWordDto privator) {
        // 手动push知识库版本
        pushVersion(operationId);
        model.put("riskContentsAssessment", false);
        if (serviceContentList.contains(LabelEnum.SMZQ.getCode())) {
            model.put("riskContentsAssessment", true);

            //5.3.3.1	总体风险分析
            RiskAnalysisReportVO riskAnalysisReportVO = iRiskAnalysisService.queryRiskAnalysisReport(operationId, true);
            RiskAnalysisReportVO.AssetStatisticsChart assetStatisticsChart =
                riskAnalysisReportVO.getAssetStatisticsChart();
            int busSystemNum = riskAnalysisReportVO.getBusSystemRiskSortChart().getXAxisList().size();
            int riskTotal = riskAnalysisReportVO.getRiskTotal();
            //  根据配置指标名称来 中风险 高风险 重大风险
            int mediumRiskAssetNum = 0;
            int highRiskAssetNum = 0;
            int moreThanHighNum = 0;
            for (ReportConfigBaseDTO dto : assetStatisticsChart.getDataList()) {
                if (dto.getConfigName().startsWith("中")) {
                    mediumRiskAssetNum = Integer.parseInt(String.valueOf(dto.getConfigIndicatorValue()));
                }
                if (dto.getConfigName().startsWith("高")) {
                    highRiskAssetNum = Integer.parseInt(String.valueOf(dto.getConfigIndicatorValue()));
                }
                if (dto.getConfigName().startsWith("重大")) {
                    moreThanHighNum = Integer.parseInt(String.valueOf(dto.getConfigIndicatorValue()));
                }
            }

            double highRiskAssetNumProportion = BigDecimal.valueOf((double)highRiskAssetNum)
                .divide(BigDecimal.valueOf((double)riskTotal), 4, BigDecimal.ROUND_HALF_DOWN)
                .multiply(BigDecimal.valueOf(100)).doubleValue();
            double mediumRiskAssetNumProportion = BigDecimal.valueOf((double)mediumRiskAssetNum)
                .divide(BigDecimal.valueOf((double)riskTotal), 4, BigDecimal.ROUND_HALF_DOWN)
                .multiply(BigDecimal.valueOf(100)).doubleValue();
            double moreThanHighNumProportion = BigDecimal.valueOf((double)moreThanHighNum)
                .divide(BigDecimal.valueOf((double)riskTotal), 4, BigDecimal.ROUND_HALF_DOWN)
                .multiply(BigDecimal.valueOf(100)).doubleValue();
            model.put("busSystemNum", busSystemNum);
            model.put("highRiskAssetNum", highRiskAssetNum);
            model.put("mediumRiskAssetNum", mediumRiskAssetNum);
            model.put("highRiskAssetNumProportion", highRiskAssetNumProportion);
            model.put("mediumRiskAssetNumProportion", mediumRiskAssetNumProportion);
            model.put("moreThanHighNumProportion", moreThanHighNumProportion);
            model.put("moreThanHighNum", moreThanHighNum);

            // 5.3.2 定性风险分析
            List<Map<String, Object>> lifecycleList = new ArrayList<>();
            List<Map<String, Object>> dataSafetyList = new ArrayList<>();
            List<Map<String, Object>> maintenanceList = new ArrayList<>();

            // 手动push知识库版本
            pushVersion(operationId);
            List<AdviseRiskDTO> adviseRisks = adviseRiskMapper.selectRiskContent(operationId);
            List<StageItemDTO> stageItems = standardItemMapper.queryStageItemIds(operationId);

            List<TreeLabelDTO> treeLabelList =
                dynamicProcessTreeMapper.selectByOperationIdAndTreeId(operationId, LabelEnum.XTDY.getCode());
            Map<Long, String> busSystemMap =
                treeLabelList.stream().collect(Collectors.toMap(TreeLabelDTO::getTreeId, TreeLabelDTO::getTreeName));
            if (CollUtil.isNotEmpty(adviseRisks)) {
                List<Tag> tagList = tagMapper.selectTagIdsByNameList(Arrays.asList("技术措施","管理措施"));
                Map<Long, String> tagMap = tagList.stream().collect(Collectors.toMap(Tag::getId,Tag::getName));
                stageItems.stream().collect(Collectors.groupingBy(StageItemDTO::getStage)).forEach((stage, items) -> {
                    if (stage.contains("数据生命周期安全防护")) {
                        lifecycleList.addAll(getRiskAnalysisList(items, adviseRisks, busSystemMap,tagMap));
                    } else if (stage.contains("数据安全组织保障")) {
                        dataSafetyList.addAll(getRiskAnalysisList(items, adviseRisks, busSystemMap, tagMap));
                    } else if (stage.contains("信息系统运维保障")) {
                        maintenanceList.addAll(getRiskAnalysisList(items, adviseRisks, busSystemMap, tagMap));
                    }
                });
                model.put("lifecycleRiskList", lifecycleList);
                model.put("dataSafetyList", dataSafetyList);
                model.put("maintenanceList", maintenanceList);
            }

            // 5.3.3 定量风险分析
            List<CoModelAnalysisResult> coModelAnalysisResultList = coModelAnalysisResultMapper
                .selectList(new QueryWrapper<CoModelAnalysisResult>().eq("operation_id", operationId));

            String titlesStr = "";
            List<Map> resultList = new ArrayList<>();
            for (CoModelAnalysisResult coModelAnalysisResult : coModelAnalysisResultList){
                titlesStr = coModelAnalysisResult.getTitles();
                List<Map> mapList = JSONUtil.toList(coModelAnalysisResult.getResult(), Map.class);
                resultList.addAll(mapList);
            }
            List<FormConfigTreeVO> titles = JSONUtil.toList(titlesStr, FormConfigTreeVO.class);
            //[{"title":"所属业务系统","dataIndex":"0","dataColumn":false,"children":[]},
            // {"title":"资产信息","dataIndex":"1","dataColumn":false,"children":[{"title":"资产名称","dataIndex":"200","width":140,"dataColumn":false,"children":[]},{"title":"敏感等级","dataIndex":"201","width":140,"dataColumn":false,"children":[]}]},
            // {"title":"数据生命周期安全安全防护","dataIndex":"2","dataColumn":false,"children":[
            // {"title":"风险值","dataIndex":"300","width":140,"dataColumn":true,"indicatorId":486,"colFormat":"NUMBER","children":[]},
            // {"title":"风险等级","dataIndex":"301","width":140,"dataColumn":true,"indicatorId":489,"colFormat":"STRING","children":[]}]},
            // {"title":"数据安全组织保障","dataIndex":"3","dataColumn":false,"children":[
            // {"title":"风险值","dataIndex":"400","width":140,"dataColumn":true,"indicatorId":487,"colFormat":"NUMBER","children":[]},
            // {"title":"风险等级","dataIndex":"401","width":140,"dataColumn":true,"indicatorId":490,"colFormat":"STRING","children":[]}]},
            // {"title":"信息系统运维保障","dataIndex":"4","dataColumn":false,"children":[
            // {"title":"风险值","dataIndex":"500","width":140,"dataColumn":true,"indicatorId":488,"colFormat":"NUMBER","children":[]},
            // {"title":"风险等级","dataIndex":"501","width":140,"dataColumn":true,"indicatorId":491,"colFormat":"STRING","children":[]}]},
            // {"title":"综合风险等级","dataIndex":"5","dataColumn":false,"children":[
            // {"title":"综合风险等级","dataIndex":"600","width":140,"dataColumn":true,"indicatorId":497,"colFormat":"STRING","children":[]}]}]
            List<String> indexList = titles.stream().flatMap(formConfigTreeVO -> formConfigTreeVO.getChildren().stream()).map(
                FormConfigTreeVO::getDataIndex).collect(Collectors.toList());
            indexList.addAll(titles.stream().map(FormConfigTreeVO::getDataIndex).collect(Collectors.toList()));
            AtomicInteger sortedIndex = new AtomicInteger(1);
            Map<String, List<InventoryAttachmentJrExcel>> busSystemInventoryMap =  resultList.stream().map(map -> InventoryAttachmentJrExcel.builder().sort(sortedIndex.getAndIncrement())
                .busSystem(MapUtil.getStr(map, indexList.get(9)))
                .assetName(MapUtil.getStr(map, indexList.get(0)))
                .sensitiveLevel(MapUtil.getStr(map, indexList.get(1)))
                .lifecycleRiskValue(MapUtil.getStr(map, indexList.get(2)))
                .lifecycleRiskLevel(MapUtil.getStr(map, indexList.get(3)))
                .dataSafetyRiskValue(MapUtil.getStr(map, indexList.get(4)))
                .dataSafetyRiskLevel(MapUtil.getStr(map, indexList.get(5)))
                .maintenanceRiskValue(MapUtil.getStr(map, indexList.get(6)))
                .maintenanceRiskLevel(MapUtil.getStr(map, indexList.get(7)))
                .totalRiskLevel(MapUtil.getStr(map, indexList.get(8))).build()).collect(Collectors.groupingBy(InventoryAttachmentJrExcel::getBusSystem ));
            List<InventoryAttachmentJrExcel> list = new ArrayList<>();
            busSystemInventoryMap.forEach((k,v) -> {
                list.addAll(CollUtil.sub(v, 0, 20));
            });
            model.put("riskList", list);
        }
    }

    private List<Map<String, Object>> getRiskAnalysisList(List<StageItemDTO> items, List<AdviseRiskDTO> adviseRisks,
        Map<Long, String> busSystemMap, Map<Long, String> tagMap) {
        List<Map<String, Object>> result = new ArrayList<>();
        Set<String> itemIdSet = items.stream().map(StageItemDTO::getItemId).collect(Collectors.toSet());
        AtomicInteger sort = new AtomicInteger(1);
        adviseRisks.stream().filter(adviseRiskDTO -> itemIdSet.contains(adviseRiskDTO.getItemId())).forEach(dto -> {
            Map<String, Object> map = new HashMap<>();
            map.put("sort", sort.getAndIncrement());
            map.put("content", dto.getContent());
            map.put("riskLevel", dto.getRiskLevel());
            map.put("riskType", CharSequenceUtil.split(dto.getTagIds(), StrPool.COMMA).stream().filter(
                tagId -> CharSequenceUtil.isNotEmpty(tagId) && tagMap.containsKey(Long.parseLong(tagId))).map(tagId -> {
                long key = Long.parseLong(tagId);
                return tagMap.get(key);
            }).collect(
                Collectors.joining()));
            map.put("describe", dto.getDescribe());
            map.put("busSystem", getBusSystem(busSystemMap, dto.getSystemId()));
            result.add(map);
        });
        return result;
    }

    private String getBusSystem(Map<Long, String> busSystemMap, String systemId) {
        String[] systemIds = systemId.split(StrPool.COMMA);
        return Arrays.stream(systemIds).map(id -> busSystemMap.get(Long.parseLong(id)))
            .collect(Collectors.joining(StrPool.COMMA));
    }

    /**
     * 合规风险分析--比例
     *
     * @param operationId
     *     request
     * @return * @return QueryViewLegalFactorVo
     * @Date 2022/9/20 17:11
     */
    private QueryViewLegalResultVo queryLegalProportion(String operationId) {
        /**
         * -------总对标文件--------------
         * 总对标(法律)文件：lawTotalNum = distinct lawName
         * 法律：article_code包含NL 统计 distinct lawName
         * 法规：article_code包含GL、DR、LL、LR、IR、RL 统计 distinct lawName
         * 标准：article_code包含 GS、NS、IS、LS、TS 统计 distinct lawName
         *--------总对标项----------------
         * 注：按item_num分组统计
         * 总对标项：itemTotalNum = group by item_num
         * 合格率：（“完全符合项”* 1+”部分符合项“*0.5）/（”总对标项“）*100%
         * -------完全符合项--------------
         * 注：按item_num分组统计
         * 完全符合项：aNum = 分组结果 = “完全符合”
         * 完全符合项占比 = aNum / itemTotalNum *100%
         * -------部分符合项--------------
         * 注：按item_num分组统计
         * -------不符合项---------
         * 注：按item_num分组统计
         * -------不涉及项（不适用）--------
         *
         */
        QueryLegalDTO retrieveLegal = new QueryLegalDTO();
        retrieveLegal.setOperationId(operationId);
        retrieveLegal.setLabelId(LabelEnum.HFHG.getCode());
        retrieveLegal.setLawName(LegalModelEnum.ZHMB.getInfo());
        return this.getLegalResultList(retrieveLegal);
    }

    private void lawAssessment(Map<String, Object> model, ExportWordDto privator, List<Long> serviceContentList,
        String operationId, List<Map<String, Object>> fileRefList, AtomicInteger fileIndex) {
        pushVersion(operationId);
        model.put("lawAssessment", false);
        if (serviceContentList.contains(LabelEnum.HFHG.getCode()) && !privator.getNeedExcludeContent()
            .contains(LabelEnum.HFHG)) {
            model.put("lawAssessment", true);

            QueryViewLegalResultVo legalResultVo = queryLegalProportion(operationId);
            model.put("lawTotalNum", legalResultVo.getLawTotalNum());
            model.put("lawDocNum", legalResultVo.getLawDocNum());
            model.put("ruleDocNum", legalResultVo.getRuleDocNum());
            model.put("standardDocNum", legalResultVo.getStandardDocNum());
            model.put("itemTotalNum", legalResultVo.getItemTotalNum());
            model.put("qualifiedProportion", legalResultVo.getQualifiedProportion());
            model.put("countANum", legalResultVo.getCountANum());
            model.put("countBNum", legalResultVo.getCountBNum());
            model.put("countCNum", legalResultVo.getCountCNum());
            model.put("countDNum", legalResultVo.getCountDNum());
            model.put("countAProportion", legalResultVo.getCountAProportion());
            model.put("countBProportion", legalResultVo.getCountBProportion());
            model.put("countCProportion", legalResultVo.getCountCProportion());
            model.put("countDProportion", legalResultVo.getCountDProportion());

            //5.2.3	具体评估结果 5.2.3.1	{{co_legal.law_name}}评估详情
            List<Map<String, Object>> lawEvaluation = Lists.newArrayList();
            QueryWrapper<CoLegal> queryWrapper2 = new QueryWrapper<>();
            queryWrapper2.eq("operation_id", privator.getOperationId());
            List<CoLegal> coLegalList = coLegalMapper.selectList(queryWrapper2);
            Map<String, List<LawRiskContentDTO>> lawRiskContentMap =  lawItemMapper.getLawRiskContentByOperationId(operationId).stream().collect(
                Collectors.groupingBy(LawRiskContentDTO::getItemId));
            
            Collator collator = Collator.getInstance(Locale.CHINA);
            if (CollUtil.isNotEmpty(coLegalList)) {
                Set<String> lawType = coLegalList.stream().map(CoLegal::getLawName).collect(Collectors.toSet());
                for (String law : lawType) {
                    AtomicInteger index = new AtomicInteger(1);
                    Map<String, Object> map = new HashMap<>(lawType.size());
                    Map<String, Object> fileMap = new HashMap<>(lawType.size());
                    map.put("lawName", law);
                    List<LawReportDTO> list = coLegalList.stream()
                        .filter(l -> Objects.equals(l.getLawName(), law) && !OptEnum.D.getInfo().equals(l.getResult()))
                        .map(legal -> LawReportDTO.builder().itemNum(StrUtil.sub(legal.getItemContent(), 0, 10))
                            .itemContent(legal.getItemContent()).itemExplain(legal.getItemExplain())
                            .result(legal.getResult()).remark(legal.getDesc()).content(getRiskContent(lawRiskContentMap.get(legal.getItemId()))).build())
                        .sorted(Comparator.comparing(i -> {
                            int sort = 9999;
                            try {
                                sort = NumberChineseFormatter.chineseToNumber(
                                    StrUtil.subBetween(i.getItemNum(), "第", "条"));
                            } catch (Exception e) {
                                if (StrUtil.containsAny(i.getItemNum(), StrUtil.DOT)) {
                                    try {
                                        sort = Integer.parseInt(StrUtil.subBefore(i.getItemNum(), StrUtil.DOT, false));
                                    } catch (Exception ignore) {
                                    }
                                }
                            }
                            return sort;
                        })).peek(dt -> dt.setSort(index.getAndIncrement())).collect(Collectors.toList());
                    map.put("legalList", list);
                    fileMap.put("name", law);
                    fileMap.put("sort", fileIndex.getAndIncrement());
                    lawEvaluation.add(map);
                    fileRefList.add(fileMap);
                }
            }
            model.put("lawEvaluation", lawEvaluation);
        }
    }

    private String getRiskContent(List<LawRiskContentDTO> lawRiskContentList) {
        if (CollUtil.isEmpty(lawRiskContentList)){
            return "";
        }
        return lawRiskContentList.stream().map(LawRiskContentDTO::getContent).filter(Objects::nonNull).distinct().collect(Collectors.joining(StrPool.LF));
    }

    /**
     * 4.现状分析
     */
    private void currentSituationAnalysis(String operationId, Map<String, Object> model,
        List<ExportWordChart> analysisChartList) {
        //查询现状核验表获取标签页
        QueryWrapper<CoVerification> query = new QueryWrapper<>();
        query.eq("operation_id", operationId);
        List<CoVerification> coVerifications = coVerificationMapper.selectList(query);
        if (CollectionUtils.isEmpty(coVerifications)) {
            return;
        }

        // 排序
        CoVerification coVerification1 = coVerifications.get(0);
        model.put("modelName", coVerification1.getModelName());

        //4.1.2.1	评估结果
        List<Map<String, Object>> analysisContents = new ArrayList<>();
        List<ExportWordChart> exportWordCharts = coGapAnalysisMapper.queryRemarkByOperationId(operationId);
        Map<String, String> remarkMap = new HashMap<>();
        if (CollUtil.isNotEmpty(exportWordCharts)) {
            remarkMap =
                exportWordCharts.stream().filter(exportWordChart -> StrUtil.isNotEmpty(exportWordChart.getRemark()))
                    .collect(Collectors.toMap(ExportWordChart::getName, ExportWordChart::getRemark, (k1, k2) -> k1));
        }
        for (ExportWordChart chart : analysisChartList) {
            if (chart.getName().startsWith("基础评估分析-")) {
                continue;
            }
            Map<String, Object> content = new HashMap<>();
            content.put("chartName", chart.getName());
            List<Map<String, Object>> chartImages = new ArrayList<>();
            Map<String, Object> chartMap = new HashMap<>();

            Map<String, String> imageMap = getPicture(Lists.newArrayList(chart));

            chartMap.put("chartImage", getPictureStream(imageMap));
            chartMap.put("chartImageName", chart.getName());
            chartMap.put("chartImageEvaluate",
                remarkMap.get(chart.getName()) == null ? "" : remarkMap.get(chart.getName()));
            chartImages.add(chartMap);
            content.put("chartImages", chartImages);
            analysisContents.add(content);
        }
        model.put("analysisContents", analysisContents);

        //*******	{{tabName1}}安全现状
        AtomicInteger sort = new AtomicInteger(1);
        model.put("lifecycleTabList",
            coVerifications.stream().filter(v -> v.getStage().contains("数据生命周期安全防护"))
                .sorted(Comparator.comparing(CoVerification::getBpCode)).peek(s -> {
                    s.setSort(sort.getAndIncrement());
                    s.setDescription(s.getDesc());
                }).collect(Collectors.toList()));
        AtomicInteger sort1 = new AtomicInteger(1);
        model.put("dataSafetyTabList",
            coVerifications.stream().filter(v -> v.getStage().contains("数据安全组织保障"))
                .sorted(Comparator.comparing(CoVerification::getBpCode)).peek(s -> {
                    s.setSort(sort1.getAndIncrement());
                    s.setDescription(s.getDesc());
                }).collect(Collectors.toList()));
        AtomicInteger sort2 = new AtomicInteger(1);
        model.put("maintenanceTabList",
            coVerifications.stream().filter(v -> v.getStage().contains("信息系统运维保障"))
                .sorted(Comparator.comparing(CoVerification::getBpCode)).peek(s -> {
                    s.setSort(sort2.getAndIncrement());
                    s.setDescription(s.getDesc());
                }).collect(Collectors.toList()));
    }

    /**
     * 默认模板需排除的章节内容
     */
    @Override
    public Set<LabelEnum> getNeedExcludeContent() {
        return CollUtil.newHashSet();
    }

    @Override
    public ReportTypeEnum getType() {
        return ReportTypeEnum.FINANCE;
    }

}
