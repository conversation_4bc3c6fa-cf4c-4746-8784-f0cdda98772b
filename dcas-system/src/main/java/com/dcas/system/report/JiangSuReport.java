package com.dcas.system.report;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.enums.DataTagEnum;
import com.dcas.common.enums.LabelEnum;
import com.dcas.common.model.dto.AdviseRiskDTO;
import com.dcas.common.model.dto.TreeLabelDTO;
import com.dcas.common.model.dto.ExportWordChart;
import com.dcas.common.model.dto.ExportWordDto;
import com.dcas.common.domain.entity.*;
import com.dcas.common.model.vo.FormConfigTreeVO;
import com.dcas.common.model.vo.QueryProjectOperationExportVo;
import com.dcas.common.mapper.*;
import com.dcas.system.report.attachment.AttachmentReportFactory;
import com.dcas.system.service.IMidOperationService;
import com.dcas.system.service.IMidThreatResultService;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.deepoove.poi.plugin.toc.TOCRenderPolicy;
import com.deepoove.poi.util.PoitlIOUtils;
import com.deepoove.poi.xwpf.NiceXWPFDocument;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 江苏报告
 *
 * <AUTHOR>
 * @date 2024/01/08 16:00
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class JiangSuReport extends AbstractReport implements ReportInterface {

    private final ModelFileMapper modelFileMapper;
    private final CoInventoryMapper coInventoryMapper;
    private final CoVerificationMapper coVerificationMapper;
    private final CoOperationMapper coOperationMapper;
    private final CoConstantMapper coConstantMapper;
    private final CoThreatAnalysisMapper coThreatAnalysisMapper;
    private final ThreatTreeMapper threatTreeMapper;
    private final CoModelAnalysisResultMapper coModelAnalysisResultMapper;
    private final AdviseRiskMapper adviseRiskMapper;
    private final CoLegalMapper coLegalMapper;
    private final IMidThreatResultService iMidThreatResultService;
    private final DynamicProcessTreeMapper dynamicProcessTreeMapper;
    private final IMidOperationService iMidOperationService;

    @Value("${safety.profile}")
    private String basePath;

    @Override
    public String exportWord(ExportWordDto dto, QueryProjectOperationExportVo poVo, Long modelId) throws Exception {
        List<String> filePathList = new ArrayList<>();
        CompletableFuture<String> future = CompletableFuture.allOf(CompletableFuture.supplyAsync(() -> {
            try {
                return process(dto, poVo, modelId);
            } catch (IOException e) {
                log.error("导出报告失败", e);
                return null;
            }
        }).whenComplete((v, th) -> {
            if (th != null) {
                log.error("", th);
            }
            if (v != null) {
                log.info(v);
                filePathList.add(v);
            }
        }), CompletableFuture.supplyAsync(() -> {
            try {
                dto.setNeedExcludeContent(getNeedExcludeContent());
                return ReportFactory.getReportHandler(ReportTypeEnum.DEFAULT).process(dto, poVo, null);
            } catch (Exception e) {
                log.error("导出报告失败", e);
                return null;
            }
        }).whenComplete((v, th) -> {
            if (th != null) {
                log.error("", th);
            }
            if (v != null) {
                log.info(v);
                filePathList.add(v);
            }
        }), CompletableFuture.supplyAsync(() -> {
            try {
                return AttachmentReportFactory.getAssetReportHandler(ReportTypeEnum.JIANGSU).exportWord(dto,
                    poVo);
            } catch (Exception e) {
                log.error("导出报告失败", e);
                return null;
            }
        }).whenComplete((v, th) -> {
            if (th != null) {
                log.error("", th);
            }
            if (v != null) {
                log.info(v);
                filePathList.add(v);
            }
        }), CompletableFuture.supplyAsync(() -> {
                try {
                    return  ReportFactory.getReportHandler(ReportTypeEnum.TEC_DETECTION).exportWord(dto, poVo);
                } catch (Exception e) {
                    log.error("导出技术检测报告失败", e);
                    return null;
                }
            }).whenComplete((v, th) -> {
                if (th != null) {
                    log.error("", th);
                }
                if (v != null) {
                    log.info(v);
                    filePathList.add(v);
                }
            }))
        .thenRun(() -> addScanReportToPath(filePathList, dto.getOperationId()))
        .thenApply(v -> zip(filePathList, poVo.getOperationName(), basePath));
        return future.get();
    }

    @Override
    @SchemaSwitch(ExportWordDto.class)
    public String process(ExportWordDto dto, QueryProjectOperationExportVo poVo, Long modelId) throws IOException {

        ModelFile modelFile = modelFileMapper.selectOne(new QueryWrapper<ModelFile>().eq("model_id", modelId));
        if (modelFile == null){
            String msg = String.format("找不到模型ID=%d对应的文件",modelId);
            throw new IOException(msg);
        }
        InputStream inputStream = new FileInputStream(modelFile.getFilePath());

        //数据模型
        Map<String, Object> model = new HashMap<>(16);

        //准备数据
        String operationId = dto.getOperationId();
        List<ExportWordChart> chartList = dto.getChartList();
        //        List<ExportWordChart> analysisChartList =
        //            chartList.stream().filter(s -> s.getName().contains("图") || s.getName().contains("表格"))
        //                .collect(Collectors.toList());

        // 报告公共部分
        putCommonInfo(poVo, model);

        List<Map<String, Object>> fileRefList = Lists.newArrayList();
        List<String> templateFileList = coVerificationMapper.selectTemplateFileByOperationId(operationId);
        if (!CollectionUtils.isEmpty(templateFileList)) {
            templateFileList.forEach(s -> {
                Map<String, Object> fileMap = new HashMap<>(1);
                fileMap.put("name", s);
                fileRefList.add(fileMap);
            });
        }
        model.put("fileRefList", fileRefList);

        //查询资产盘点表
        QueryWrapper<CoInventory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", dto.getOperationId());
        queryWrapper.orderByDesc("bus_system");
        List<CoInventory> coInventoryList = coInventoryMapper.selectList(queryWrapper);
        List<String> busSystemList =
            coInventoryList.stream().map(CoInventory::getBusSystem).distinct().collect(Collectors.toList());

        // 数据安全风险汇总表
        List<Map<String, Object>> riskLevelList = new ArrayList<>();
        Map<String, Object> riskModel = new HashMap<>(16);
        riskModel.put("sort", "");
        riskModel.put("desc", "");
        riskModel.put("level", "");
        riskLevelList.add(riskModel);
        model.put("riskLevelList", riskLevelList);

        // 数据识别
        //3.资产梳理
        setModelAssetInfo(model, coInventoryList, operationId);
        List<ExportWordChart> sensitiveChartList =
            chartList.stream().filter(s -> "敏感数据占比".equals(s.getName())).collect(Collectors.toList());
        Map<String, String> imageBase64Data1 = getPicture(sensitiveChartList);
        putImage("sensitiveAssetsProportionImg", imageBase64Data1, model);

        //威胁赋值清单
        List<TreeLabelDTO> treeLabelList = dynamicProcessTreeMapper.selectByOperationIdAndTreeId(operationId, LabelEnum.XTDY.getCode());
        Map<String, Long> busSystemIdMap = treeLabelList.stream().collect(Collectors.toMap(TreeLabelDTO::getTreeName,TreeLabelDTO::getTreeId));
        List<CoThreatAnalysis> coThreatAnalyses =
            coThreatAnalysisMapper.selectList(new QueryWrapper<CoThreatAnalysis>().eq("operation_id", operationId));
        List<Map<String, Object>> threatList = new ArrayList<>();
        List<MidThreatResult> midThreatResultList = iMidThreatResultService.lambdaQuery().eq(MidThreatResult::getOperationId, operationId).list();
        Map<Long, List<MidThreatResult>> midThreatMap = midThreatResultList.stream().collect(Collectors.groupingBy(MidThreatResult::getSystemId));
        List<MidOperation> midOperationList = iMidOperationService.lambdaQuery().eq(MidOperation::getOperationId, operationId).list();
        Map<String, MidOperation> buySystemOperationMap = midOperationList.stream().collect(Collectors.toMap(MidOperation::getBusSystem, Function.identity(), (k1,k2)->k1));
        if (CollUtil.isNotEmpty(coThreatAnalyses)) {
            AtomicInteger sort = new AtomicInteger(1);
            coThreatAnalyses.stream()
                .collect(Collectors.toMap(CoThreatAnalysis::getBusSystem, Function.identity(), (k1, k2) -> k1))
                .forEach((k, v) -> {
                    // 正常一个业务系统配置一个威胁频率赋值
                    Map<String, Object> threatValueModel = new HashMap<>(16);
                    threatValueModel.put("sort", sort.get());
                    threatValueModel.put("busSystem", k);
                    threatValueModel.put("threatType", threatTreeMapper.selectThreatTreeListByType(v.getThreatType()));
                    Long busSystemId = busSystemIdMap.get(k);
                    List<MidThreatResult> midThreatResults = midThreatMap.get(busSystemId);
                    double sumLevel = midThreatResults.stream().mapToDouble(midThreatResult -> midThreatResult.getThreatFrequencyLevel().doubleValue()).sum();
                    // 计算公式：SUM(选中的威胁分类等级之和)/CT(选中的威胁分类数量之和)*最大威胁等级
                    MidOperation midOperation = buySystemOperationMap.get(k);
                    Integer frequency = (int)Math.ceil(sumLevel/midThreatResults.size()*midOperation.getMaxThreatLevel());
                    threatValueModel.put("threatFrequency", getThreatLevelMap().get(frequency));
                    threatValueModel.put("threatValue", frequency);
                    threatList.add(threatValueModel);
                    sort.getAndIncrement();
                });
        }
        model.put("threatList", threatList);

        // 阶段
        List<CoVerification> coVerifications = coVerificationMapper.queryVerificationList(operationId, "江苏能力评估模板");

        // 5.1管理脆弱性 6.1措施
        setModelFiveAndSix(model, coVerifications, operationId);

        // 7.1风险计算 7.2.2综合分析
        setModelSeven(model, busSystemList, operationId, modelId, chartList, coVerifications);

        // 7.2.1风险评估结论
        riskAnalysis(model,operationId);

        // 8.1风险控制建议
        processSuggestions(operationId, model);

        //配置模板
        LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
        TOCRenderPolicy tocPolicy = new TOCRenderPolicy();
        Configure config =
            Configure.builder().bind("riskLevelList", policy).bind("threatList", policy).bind("riskCalcResult", policy)
                .bind("dataSaftyHighSuggests", policy).bind("dataTecHighSuggests", policy)
                .bind("dataSaftyMidSuggests", policy).bind("dataTecMidSuggests", policy)
                .bind("dataSafeManagerList", policy).bind("personSafeManagerList", policy)
                .bind("dataSafeMergencyList", policy).bind("dataCollectList", policy).bind("dataTranslateList", policy)
                .bind("dataStorageList", policy).bind("dataProvisionList", policy).bind("dataUseList", policy)
                .bind("dataDisclosureList", policy).bind("dataDeleteList", policy).bind("dataExportList", policy)
                .bind("dataSafeManagerDescList", policy).bind("dataSafeTecDescList", policy)
                .bind("tocContents", tocPolicy).bind("managerRiskAnalysisList", policy)
                .bind("tecRiskAnalysisList", policy).build();

        //加载模板渲染数据
        XWPFTemplate template = XWPFTemplate.compile(inputStream, config).render(model);
        NiceXWPFDocument xwpfDocument = template.getXWPFDocument();
        xwpfDocument.enforceUpdateFields();

        String realFileName = String.format("%s报告-江苏专用.docx", poVo.getOperationName());
        String path = String.join(File.separator, basePath, "temp", realFileName);
        FileUtil.touch(path);

        //输出结果
        OutputStream out = new FileOutputStream(path);
        BufferedOutputStream bos = new BufferedOutputStream(out);
        xwpfDocument.write(bos);
        bos.flush();
        out.flush();
        PoitlIOUtils.closeQuietlyMulti(template, xwpfDocument, bos, out);
        return path;
    }

    private void riskAnalysis(Map<String, Object> model, String operationId) {
        // 获取合规模板关联法律法规文件ID
        List<AdviseRiskDTO> adviseRisks = adviseRiskMapper.selectRiskContentWithLawIds(operationId);

        if (CollUtil.isNotEmpty(adviseRisks)){
            List<String> itemIds = adviseRisks.stream().map(AdviseRiskDTO::getItemId).collect(
                Collectors.toList());
            // 获取合规依据
            List<AdviseRiskDTO> lawBasics = adviseRiskMapper.selectLawBasicByItemIds(itemIds, operationId);
            // 获取能力依据
            List<AdviseRiskDTO> analysisBasics = adviseRiskMapper.selectAnalysisBasicByItemIds(itemIds, operationId);
            List<AdviseRiskDTO> allBasics = new ArrayList<>();
            allBasics.addAll(lawBasics);
            allBasics.addAll(analysisBasics);
            Map<String, String> itemBasicMap = allBasics.stream().collect(Collectors.groupingBy(AdviseRiskDTO::getItemId, Collectors.mapping(AdviseRiskDTO::getBasis, Collectors.joining(", "))));
            adviseRisks.stream().filter(adviseRiskDTO -> adviseRiskDTO.getContent() != null).collect(Collectors.groupingBy(AdviseRiskDTO::getRiskType)).forEach((k,v)->{
                if (k.contains("管理")){
                    model.put("managerRiskAnalysisList", getRiskAnalysisList(v, itemBasicMap));
                } else {
                    model.put("tecRiskAnalysisList", getRiskAnalysisList(v, itemBasicMap));
                }
            });
        }
    }

    private List<Map<String, Object>> getRiskAnalysisList(List<AdviseRiskDTO> v, Map<String, String> itemBasicMap) {
        AtomicInteger sort = new AtomicInteger(1);
        List<Map<String, Object>> result = new ArrayList<>();
        v.stream().collect(Collectors.groupingBy(AdviseRiskDTO::getContent)).forEach((k,list)->{
            Map<String, Object> map = new HashMap<>(16);
            Set<String> set = new HashSet<>();
            Set<String> pointSet = new HashSet<>();
            for (AdviseRiskDTO adviseRiskDTO : list){
                if (CharSequenceUtil.isNotEmpty(adviseRiskDTO.getDescribe())){
                    pointSet.add(adviseRiskDTO.getDescribe());
                }
                String basis = itemBasicMap.get(adviseRiskDTO.getItemId());
                if (CharSequenceUtil.isEmpty(basis)){
                    continue;
                }
                basis = basis.replace(" ", "");
                if (basis.contains("，")) {
                    set.addAll(Arrays.asList(basis.split("，")));
                } else if (basis.contains(StrPool.COMMA)) {
                    set.addAll(Arrays.asList(basis.split(StrPool.COMMA)));
                } else {
                    set.add(basis);
                }
            }
            if (!set.isEmpty()) {
                map.put("sort", sort.getAndIncrement());
                map.put("riskAnalysis", k);
                String basis = set.stream().filter(Objects::nonNull).collect(Collectors.joining(StrPool.LF));
                map.put("riskBasic", basis);
                map.put("riskPoint", CollUtil.join(pointSet, StrPool.COMMA));
                result.add(map);
            }
        });
        return result;
    }

    private Map<Integer, String> getThreatLevelMap() {
        Map<Integer, String> threatLevelMap = new HashMap<>(16);
        threatLevelMap.put(5, "很高");
        threatLevelMap.put(4, "高");
        threatLevelMap.put(3, "中等");
        threatLevelMap.put(2, "低");
        threatLevelMap.put(1, "很低");
        return threatLevelMap;
    }

    private void setModelAssetInfo(Map<String, Object> model, List<CoInventory> coInventories, String operationId) {
        //数据资产总条数
        int dataAssetsTotal = coInventories.size();
        //高敏感资产
        List<CoInventory> highSensitiveAssetsList = new ArrayList<>();
        //中敏感资产
        List<CoInventory> midSensitiveAssetsList = new ArrayList<>();
        //低敏感资产
        List<CoInventory> lowSensitiveAssetsList = new ArrayList<>();
        QueryWrapper<CoConstant> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("customer_id", coOperationMapper.selectCustomIdByOperationId(operationId));
        List<CoConstant> coConstants = coConstantMapper.selectList(queryWrapper);
        //客户最高敏感等级
        int level = coConstants.get(0).getHighestSensitiveLevel();
        //客户最高敏感等级
        sensitiveAssetProcess(level, coInventories, highSensitiveAssetsList, midSensitiveAssetsList,
            lowSensitiveAssetsList);

        model.put("dataAssetsNum", dataAssetsTotal);
        model.put("assetCount", dataAssetsTotal);

        // 核心、重要、一般资产数量
        if (CollUtil.isNotEmpty(coInventories)) {
            int coreData = 0;
            int majorData = 0;
            int generalData = 0;
            for (CoInventory coInventory : coInventories) {
                if (Objects.equals(coInventory.getDataTag(), DataTagEnum.CORE.getTag())) {
                    coreData++;
                } else if (Objects.equals(coInventory.getDataTag(), DataTagEnum.IMPORTANT.getTag())) {
                    majorData++;
                } else if (Objects.equals(coInventory.getDataTag(), DataTagEnum.GENERAL.getTag())) {
                    generalData++;
                }
            }
            model.put("coreData", coreData);
            model.put("majorData", majorData);
            model.put("generalData", generalData);
        }

        // 最高敏感级别
        int maxLevel =
            coInventories.stream().map(CoInventory::getSensitiveLevel).max(Comparator.comparingInt(s -> s)).orElse(0);
        model.put("maxLevel", maxLevel);
        model.put("level", level);
    }

    private void setModelSeven(Map<String, Object> model, List<String> busSystemList, String operationId, Long modelId,
        List<ExportWordChart> chartList, List<CoVerification> coVerifications) {
        List<RiskResult> riskCalcResult = new ArrayList<>();
        List<CoModelAnalysisResult> coModelAnalysisResultList = coModelAnalysisResultMapper
            .selectList(new QueryWrapper<CoModelAnalysisResult>().eq("operation_id", operationId));

        String titlesStr = "";
        List<Map> resultList = new ArrayList<>();
        for (CoModelAnalysisResult coModelAnalysisResult : coModelAnalysisResultList){
            titlesStr = coModelAnalysisResult.getTitles();
            List<Map> mapList = JSONUtil.toList(coModelAnalysisResult.getResult(), Map.class);
            resultList.addAll(mapList);
        }
        List<FormConfigTreeVO> titles = JSONUtil.toList(titlesStr, FormConfigTreeVO.class);
        List<String> indexList = titles.stream().map(FormConfigTreeVO::getDataIndex).collect(Collectors.toList());
        // 高风险系统map
        Set<String> highRiskLevelSystemSet = new HashSet<>();
        // 中风险系统map
        Set<String> midRiskLevelSystemSet = new HashSet<>();
        // 低风险系统map
        Set<String> lowRiskLevelSystemSet = new HashSet<>();
        Set<String> otherSystemSet = new HashSet<>();
        // 高风险阶段map
        Set<String> highRiskLevelStageSet = new HashSet<>();
        Set<String> midRiskLevelStageSet = new HashSet<>();
        Set<String> lowRiskLevelStageSet = new HashSet<>();
        Set<String> otherStageSet = new HashSet<>();
        // 很高风险资产数量
        AtomicInteger veryHighRiskNum = new AtomicInteger();
        // 高风险资产数量
        AtomicInteger highRiskNum = new AtomicInteger();
        // 中风险资产数量
        AtomicInteger midRiskNum = new AtomicInteger();
        // 低风险资产数量
        AtomicInteger lowRiskNum = new AtomicInteger();
        // 很低风险资产数量
        AtomicInteger veryLowRiskNum = new AtomicInteger();
        AtomicInteger total = new AtomicInteger();
        List<String> riskValueList = new ArrayList<>();
        resultList.forEach(map -> {
            RiskResult result = new RiskResult();
            String busSystem = String.valueOf(map.get(indexList.get(0)));
            result.setBusSystem(busSystem);
            result.setName((String)map.get("200"));
//            result.setComment(map.get(indexList.get(2)) != null ? String.valueOf(map.get(indexList.get(2))) : null);
            result.setThreatType(map.get(indexList.get(2)) != null ? (String)map.get(indexList.get(2)) : null);
            String process = String.valueOf(map.get(indexList.get(4)));
            result.setProcess(process);
            result.setLevel(map.get(indexList.get(4))!=null ? (String)map.get(indexList.get(4)) : null);
            result.setT(map.get(indexList.get(5)) != null ? (String)map.get(indexList.get(5)) : null);
            result.setV(map.get(indexList.get(6)) != null ? (String)map.get(indexList.get(6)) : null);
            result.setB(map.get(indexList.get(7)) != null ? (String)map.get(indexList.get(7)) : null);
            result.setL(map.get(indexList.get(8)) != null ? (String)map.get(indexList.get(8)): null);
            result.setF(map.get(indexList.get(9)) != null ? (String)map.get(indexList.get(9)) : null);
            String riskValue = (String)map.get(indexList.get(10));
            result.setRn(riskValue);
            riskValueList.add(riskValue);
            String riskLevel = ((String)map.get(indexList.get(11)));
            result.setRiskLevel(riskLevel);
            List<String> processList = StrUtil.split(process, StrUtil.COMMA);
            if ("5".equals(riskLevel) || "很高".equals(riskLevel)) {
                highRiskLevelSystemSet.add(busSystem);
                highRiskLevelStageSet.addAll(processList);
                veryHighRiskNum.getAndIncrement();
            } else if ("4".equals(riskLevel) || "高".equals(riskLevel)) {
                highRiskLevelSystemSet.add(busSystem);
                highRiskLevelStageSet.addAll(processList);
                highRiskNum.getAndIncrement();
            } else if ("3".equals(riskLevel) || "中".equals(riskLevel)) {
                midRiskLevelSystemSet.add(busSystem);
                midRiskLevelStageSet.addAll(processList);
                midRiskNum.getAndIncrement();
            } else if ("2".equals(riskLevel) || "低".equals(riskLevel)) {
                lowRiskLevelSystemSet.add(busSystem);
                lowRiskLevelStageSet.addAll(processList);
                lowRiskNum.getAndIncrement();
            } else if ("1".equals(riskLevel) || "很低".equals(riskLevel)) {
                lowRiskLevelSystemSet.add(busSystem);
                lowRiskLevelStageSet.addAll(processList);
                veryLowRiskNum.getAndIncrement();
            } else {
                otherSystemSet.add(busSystem);
            }
            total.incrementAndGet();
            riskCalcResult.add(result);
        });
        model.put("riskCalcResult",
            riskCalcResult.stream().sorted(Comparator.comparing(RiskResult::getRiskLevel).reversed()).limit(100)
                .collect(Collectors.toList()));

        model.put("highBusSystem", StrUtil.join("、", highRiskLevelSystemSet));
        model.put("highStage", StrUtil.join("、", highRiskLevelStageSet));
        model.put("highStageExist", highRiskLevelStageSet.size() > 0);
        model.put("midBusSystem", StrUtil.join("、", midRiskLevelSystemSet));
        model.put("midStage", StrUtil.join("、", midRiskLevelStageSet));
        model.put("midStageExist", midRiskLevelStageSet.size() > 0);
        model.put("lowBusSystem", StrUtil.join("、", lowRiskLevelSystemSet));
        model.put("lowStage", StrUtil.join("、", lowRiskLevelStageSet));
        model.put("lowStageExist", lowRiskLevelStageSet.size() > 0);
        otherStageSet = coVerifications.stream().filter(
            coVerification -> coVerification != null && (!highRiskLevelStageSet.contains(coVerification.getProcess())
                && !midRiskLevelStageSet.contains(coVerification.getProcess())
                && !lowRiskLevelStageSet.contains(coVerification.getProcess())))
            .map(CoVerification::getProcess).collect(Collectors.toSet());
        model.put("exist", CollUtil.isNotEmpty(otherStageSet));
        model.put("stage", StrUtil.join("、", otherStageSet));
        model.put("highRiskStage", StrUtil.join("、", highRiskLevelStageSet));
        model.put("midRiskStage", StrUtil.join("、", midRiskLevelStageSet));
        model.put("veryHighRiskNum", veryHighRiskNum.get());
        model.put("highRiskNum", highRiskNum.get());
        model.put("midRiskNum", midRiskNum.get());
        model.put("lowRiskNum", lowRiskNum.get());
        model.put("veryLowRiskNum", veryLowRiskNum.get());
        model.put("busSystemNum", busSystemList.size());

        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        int highRiskAssetNum = veryHighRiskNum.addAndGet(highRiskNum.get());
        int mediumRiskAssetNum = midRiskNum.get();
        model.put("highRiskAssetNum", highRiskAssetNum);
        model.put("mediumRiskAssetNum", mediumRiskAssetNum);
        // 高风险资产占比
        String highRiskAssetNumProportion = numberFormat.format(
            BigDecimal.valueOf(highRiskAssetNum).divide(BigDecimal.valueOf(total.get()), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100)).doubleValue());
        // 中风险资产占比
        String mediumRiskAssetNumProportion = numberFormat.format(
            BigDecimal.valueOf(mediumRiskAssetNum).divide(BigDecimal.valueOf(total.get()), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100)).doubleValue());
        model.put("highRiskAssetNumProportion", highRiskAssetNumProportion);
        model.put("mediumRiskAssetNumProportion", mediumRiskAssetNumProportion);
        String totalRiskValue = numberFormat.format(
            BigDecimal.valueOf(riskValueList.stream().mapToDouble(Double::parseDouble).sum())
                .divide(BigDecimal.valueOf(riskValueList.size()), 2, RoundingMode.HALF_UP).doubleValue());
        model.put("totalRiskValue", totalRiskValue);
        model.put("totalRiskLevel", getTotalRiskLevel(totalRiskValue));

        List<ExportWordChart> chartList1 =
            chartList.stream().filter(s -> "业务系统风险一览".equals(s.getName())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(chartList1)) {
            model.put("queryLifecycleReslutImg", getPictureStream(getPicture(chartList1)));
        }
    }

    private String getTotalRiskLevel(String totalRiskValue) {
        //        0.5 =< js_risk_level  < 187.5      1
        //        187.5 =< js_risk_level  < 375    2
        //        375 =< js_risk_level  < 562.5      3
        //        562.5 =< js_risk_level  < 750     4
        //        750 =< js_risk_level  =< 937.5      5
        Double value = Double.parseDouble(totalRiskValue);
        if (0.5 <= value && value < 187.5) {
            return "很低";
        } else if (187.5 <= value && value < 375) {
            return "低";
        } else if (375 <= value && value < 562.5) {
            return "中等";
        } else if (562.5 <= value && value < 750) {
            return "高";
        } else {
            return "很高";
        }
    }

    private void setModelFiveAndSix(Map<String, Object> model, List<CoVerification> coVerifications,
        String operationId) {
        model.put("stageName", coVerifications.stream()
            .filter(coVerification -> coVerification != null && !"不适用".equals(coVerification.getResult()))
            .map(CoVerification::getProcess).distinct().collect(Collectors.joining("、")));

        //现状核验 调研内容项：符合性说明。
        // 1.数据安全管理组织-人员安全管理-数据安全应急管理
        //        List<Map<String, Object>> dataSafeManagerDescList = new ArrayList<>();
        // 2.数据收集-数据传输-数据存储-数据提供-数据使用和加工-数据公开-数据删除-数据出境
        //        List<Map<String, Object>> dataSafeTecDescList = new ArrayList<>();

        Map<String, String> descMap = getBpCodeDescribe(operationId);
        // 核查项全部符合和不涉及  不存在
        // 部分符合和不符合  存在
        coVerifications.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(CoVerification::getProcess))
            .forEach((k, v) -> {
                switch (k) {
                    case "数据安全管理组织":
                        // 数据安全管理组织
                        List<Map<String, Object>> dataSafeManagerList = new ArrayList<>();
                        List<Map<String, Object>> dataSafeManagerDescList = new ArrayList<>();
                        v.stream().sorted(Comparator.comparing(CoVerification::getBpCode)).forEach(coVerification -> {
                            Map<String, Object> dataSafeManager = new HashMap<>(16);
                            dataSafeManager.put("code", coVerification.getBpCode());
                            dataSafeManager.put("content", coVerification.getStandardProvision());
                            dataSafeManager.put("result", ("不适用".equals(coVerification.getResult()) || "全部符合"
                                .equals(coVerification.getResult())) ? "不存在" : "存在");
                            dataSafeManagerList.add(dataSafeManager);

                            Map<String, Object> dataSafeManagerDesc = new HashMap<>(16);
                            dataSafeManagerDesc.put("item", coVerification.getStandardProvision());
                            String desc = StrUtil.isNotEmpty(coVerification.getDescription()) ? coVerification.getDescription() : descMap.get(coVerification.getBpCode());
                            dataSafeManagerDesc.put("desc", desc == null ? "暂无安全措施" : desc);
                            model.put("desc" + StrUtil.replace(coVerification.getBpCode(), ".", ""),
                                desc == null ? "暂无安全措施" : desc);
                            model.put("result" + StrUtil.replace(coVerification.getBpCode(), ".", ""),
                                ("不适用".equals(coVerification.getResult()) || "全部符合"
                                    .equals(coVerification.getResult())) ? "不存在" : "存在");
                            dataSafeManagerDescList.add(dataSafeManagerDesc);
                        });
                        model.put("dataSafeManagerList", dataSafeManagerList);
                        model.put("dataSafeManagerDescList", dataSafeManagerDescList);
                        break;
                    case "人员安全管理":
                        // 人员管理组织
                        List<Map<String, Object>> personSafeManagerList = new ArrayList<>();
                        List<Map<String, Object>> personSafeManagerDescList = new ArrayList<>();
                        v.stream().sorted(Comparator.comparing(CoVerification::getBpCode)).forEach(coVerification -> {
                            Map<String, Object> personSafeManager = new HashMap<>(16);
                            personSafeManager.put("code", coVerification.getBpCode());
                            personSafeManager.put("content", coVerification.getStandardProvision());
                            personSafeManager.put("result", ("不适用".equals(coVerification.getResult()) || "全部符合"
                                .equals(coVerification.getResult())) ? "不存在" : "存在");
                            personSafeManagerList.add(personSafeManager);

                            Map<String, Object> dataSafeManagerDesc = new HashMap<>(16);
                            dataSafeManagerDesc.put("item", coVerification.getStandardProvision());
                            String desc = StrUtil.isNotEmpty(coVerification.getDescription()) ? coVerification.getDescription() : descMap.get(coVerification.getBpCode());
                            dataSafeManagerDesc.put("desc", desc == null ? "暂无安全措施" : desc);
                            model.put("desc" + StrUtil.replace(coVerification.getBpCode(), ".", ""),
                                desc == null ? "暂无安全措施" : desc);
                            model.put("result" + StrUtil.replace(coVerification.getBpCode(), ".", ""),
                                ("不适用".equals(coVerification.getResult()) || "全部符合"
                                    .equals(coVerification.getResult())) ? "不存在" : "存在");
                            personSafeManagerDescList.add(dataSafeManagerDesc);
                        });
                        model.put("personSafeManagerList", personSafeManagerList);
                        model.put("personSafeManagerDescList", personSafeManagerDescList);
                        break;
                    case "数据安全应急管理":
                        // 数据安全应急管理
                        List<Map<String, Object>> dataSafeMergencyList = new ArrayList<>();
                        List<Map<String, Object>> dataSafeMergencyDescList = new ArrayList<>();
                        v.stream().sorted(Comparator.comparing(CoVerification::getBpCode)).forEach(coVerification -> {
                            Map<String, Object> dataSafeMergency = new HashMap<>(16);
                            dataSafeMergency.put("code", coVerification.getBpCode());
                            dataSafeMergency.put("content", coVerification.getStandardProvision());
                            dataSafeMergency.put("result", ("不适用".equals(coVerification.getResult()) || "全部符合"
                                .equals(coVerification.getResult())) ? "不存在" : "存在");
                            dataSafeMergencyList.add(dataSafeMergency);

                            Map<String, Object> dataSafeManagerDesc = new HashMap<>(16);
                            dataSafeManagerDesc.put("item", coVerification.getStandardProvision());
                            String desc = StrUtil.isNotEmpty(coVerification.getDescription()) ? coVerification.getDescription() : descMap.get(coVerification.getBpCode());
                            dataSafeManagerDesc.put("desc", desc == null ? "暂无安全措施" : desc);
                            model.put("desc" + StrUtil.replace(coVerification.getBpCode(), ".", ""),
                                desc == null ? "暂无安全措施" : desc);
                            model.put("result" + StrUtil.replace(coVerification.getBpCode(), ".", ""),
                                ("不适用".equals(coVerification.getResult()) || "全部符合"
                                    .equals(coVerification.getResult())) ? "不存在" : "存在");
                            dataSafeMergencyDescList.add(dataSafeManagerDesc);
                        });
                        model.put("dataSafeMergencyList", dataSafeMergencyList);
                        model.put("dataSafeMergencyDescList", dataSafeMergencyDescList);
                        break;
                    case "数据收集":
                        // 技术脆弱性识别
                        List<Map<String, Object>> dataCollectList = new ArrayList<>();
                        List<Map<String, Object>> dataCollectDescList = new ArrayList<>();
                        v.stream().sorted(Comparator.comparing(CoVerification::getBpCode)).forEach(coVerification -> {
                            Map<String, Object> dataCollect = new HashMap<>(16);
                            dataCollect.put("code", coVerification.getBpCode());
                            dataCollect.put("content", coVerification.getStandardProvision());
                            dataCollect.put("result", ("不适用".equals(coVerification.getResult()) || "全部符合"
                                .equals(coVerification.getResult())) ? "不存在" : "存在");
                            dataCollectList.add(dataCollect);

                            Map<String, Object> dataSafeTecDesc = new HashMap<>(16);
                            dataSafeTecDesc.put("item", coVerification.getStandardProvision());
                            String desc = StrUtil.isNotEmpty(coVerification.getDescription()) ? coVerification.getDescription() : descMap.get(coVerification.getBpCode());
                            dataSafeTecDesc.put("desc", desc == null ? "暂无安全措施" : desc);
                            model.put("desc" + StrUtil.replace(coVerification.getBpCode(), ".", ""),
                                desc == null ? "暂无安全措施" : desc);
                            model.put("result" + StrUtil.replace(coVerification.getBpCode(), ".", ""),
                                ("不适用".equals(coVerification.getResult()) || "全部符合"
                                    .equals(coVerification.getResult())) ? "不存在" : "存在");
                            dataCollectDescList.add(dataSafeTecDesc);
                        });
                        model.put("dataCollectList", dataCollectList);
                        model.put("dataCollectDescList", dataCollectDescList);
                        break;
                    case "数据传输":
                        // 数据传输
                        List<Map<String, Object>> dataTranslateList = new ArrayList<>();
                        List<Map<String, Object>> dataTranslateDescList = new ArrayList<>();
                        v.stream().sorted(Comparator.comparing(CoVerification::getBpCode)).forEach(coVerification -> {
                            Map<String, Object> dataTranslate = new HashMap<>(16);
                            dataTranslate.put("code", coVerification.getBpCode());
                            dataTranslate.put("content", coVerification.getStandardProvision());
                            dataTranslate.put("result", ("不适用".equals(coVerification.getResult()) || "全部符合"
                                .equals(coVerification.getResult())) ? "不存在" : "存在");
                            dataTranslateList.add(dataTranslate);

                            Map<String, Object> dataSafeTecDesc = new HashMap<>(16);
                            dataSafeTecDesc.put("item", coVerification.getStandardProvision());
                            String desc = StrUtil.isNotEmpty(coVerification.getDescription()) ? coVerification.getDescription() : descMap.get(coVerification.getBpCode());
                            dataSafeTecDesc.put("desc", desc == null ? "暂无安全措施" : desc);
                            model.put("desc" + StrUtil.replace(coVerification.getBpCode(), ".", ""),
                                desc == null ? "暂无安全措施" : desc);
                            model.put("result" + StrUtil.replace(coVerification.getBpCode(), ".", ""),
                                ("不适用".equals(coVerification.getResult()) || "全部符合"
                                    .equals(coVerification.getResult())) ? "不存在" : "存在");
                            dataTranslateDescList.add(dataSafeTecDesc);
                        });
                        model.put("dataTranslateList", dataTranslateList);
                        model.put("dataTranslateDescList", dataTranslateDescList);
                        break;
                    case "数据存储":
                        // 数据存储
                        List<Map<String, Object>> dataStorageList = new ArrayList<>();
                        List<Map<String, Object>> dataStorageDescList = new ArrayList<>();
                        v.stream().sorted(Comparator.comparing(CoVerification::getBpCode)).forEach(coVerification -> {
                            Map<String, Object> dataStorage = new HashMap<>(16);
                            dataStorage.put("code", coVerification.getBpCode());
                            dataStorage.put("content", coVerification.getStandardProvision());
                            dataStorage.put("result", ("不适用".equals(coVerification.getResult()) || "全部符合"
                                .equals(coVerification.getResult())) ? "不存在" : "存在");
                            dataStorageList.add(dataStorage);

                            Map<String, Object> dataSafeTecDesc = new HashMap<>(16);
                            dataSafeTecDesc.put("item", coVerification.getStandardProvision());
                            String desc = StrUtil.isNotEmpty(coVerification.getDescription()) ? coVerification.getDescription() : descMap.get(coVerification.getBpCode());
                            dataSafeTecDesc.put("desc", desc == null ? "暂无安全措施" : desc);
                            model.put("desc" + StrUtil.replace(coVerification.getBpCode(), ".", ""),
                                desc == null ? "暂无安全措施" : desc);
                            model.put("result" + StrUtil.replace(coVerification.getBpCode(), ".", ""),
                                ("不适用".equals(coVerification.getResult()) || "全部符合"
                                    .equals(coVerification.getResult())) ? "不存在" : "存在");
                            dataStorageDescList.add(dataSafeTecDesc);
                        });
                        model.put("dataStorageList", dataStorageList);
                        model.put("dataStorageDescList", dataStorageDescList);
                        break;
                    case "数据提供":
                        // 数据提供
                        List<Map<String, Object>> dataProvisionList = new ArrayList<>();
                        List<Map<String, Object>> dataProvisionDescList = new ArrayList<>();
                        v.stream().sorted(Comparator.comparing(CoVerification::getBpCode)).forEach(coVerification -> {
                            Map<String, Object> dataProvision = new HashMap<>(16);
                            dataProvision.put("code", coVerification.getBpCode());
                            dataProvision.put("content", coVerification.getStandardProvision());
                            dataProvision.put("result", ("不适用".equals(coVerification.getResult()) || "全部符合"
                                .equals(coVerification.getResult())) ? "不存在" : "存在");
                            dataProvisionList.add(dataProvision);

                            Map<String, Object> dataSafeTecDesc = new HashMap<>(16);
                            dataSafeTecDesc.put("item", coVerification.getStandardProvision());
                            String desc = StrUtil.isNotEmpty(coVerification.getDescription()) ? coVerification.getDescription() : descMap.get(coVerification.getBpCode());
                            dataSafeTecDesc.put("desc", desc == null ? "暂无安全措施" : desc);
                            model.put("desc" + StrUtil.replace(coVerification.getBpCode(), ".", ""),
                                desc == null ? "暂无安全措施" : desc);
                            model.put("result" + StrUtil.replace(coVerification.getBpCode(), ".", ""),
                                ("不适用".equals(coVerification.getResult()) || "全部符合"
                                    .equals(coVerification.getResult())) ? "不存在" : "存在");
                            dataProvisionDescList.add(dataSafeTecDesc);
                        });
                        model.put("dataProvisionList", dataProvisionList);
                        model.put("dataProvisionDescList", dataProvisionDescList);
                        break;
                    case "数据使用和加工":
                        //数据使用和加工
                        List<Map<String, Object>> dataUseList = new ArrayList<>();
                        List<Map<String, Object>> dataUseDescList = new ArrayList<>();
                        v.stream().sorted(Comparator.comparing(CoVerification::getBpCode)).forEach(coVerification -> {
                            Map<String, Object> dataUse = new HashMap<>(16);
                            dataUse.put("code", coVerification.getBpCode());
                            dataUse.put("content", coVerification.getStandardProvision());
                            dataUse.put("result", ("不适用".equals(coVerification.getResult()) || "全部符合"
                                .equals(coVerification.getResult())) ? "不存在" : "存在");
                            dataUseList.add(dataUse);

                            Map<String, Object> dataSafeTecDesc = new HashMap<>(16);
                            dataSafeTecDesc.put("item", coVerification.getStandardProvision());
                            String desc = StrUtil.isNotEmpty(coVerification.getDescription()) ? coVerification.getDescription() : descMap.get(coVerification.getBpCode());
                            dataSafeTecDesc.put("desc", desc == null ? "暂无安全措施" : desc);
                            model.put("desc" + StrUtil.replace(coVerification.getBpCode(), ".", ""),
                                desc == null ? "暂无安全措施" : desc);
                            model.put("result" + StrUtil.replace(coVerification.getBpCode(), ".", ""),
                                ("不适用".equals(coVerification.getResult()) || "全部符合"
                                    .equals(coVerification.getResult())) ? "不存在" : "存在");
                            dataUseDescList.add(dataSafeTecDesc);
                        });
                        model.put("dataUseList", dataUseList);
                        model.put("dataUseDescList", dataUseDescList);
                        break;
                    case "数据公开":
                        // 数据公开
                        List<Map<String, Object>> dataDisclosureList = new ArrayList<>();
                        List<Map<String, Object>> dataDisclosureDescList = new ArrayList<>();
                        v.stream().sorted(Comparator.comparing(CoVerification::getBpCode)).forEach(coVerification -> {
                            Map<String, Object> dataDisclosure = new HashMap<>(16);
                            dataDisclosure.put("code", coVerification.getBpCode());
                            dataDisclosure.put("content", coVerification.getStandardProvision());
                            dataDisclosure.put("result", ("不适用".equals(coVerification.getResult()) || "全部符合"
                                .equals(coVerification.getResult())) ? "不存在" : "存在");
                            dataDisclosureList.add(dataDisclosure);

                            Map<String, Object> dataSafeTecDesc = new HashMap<>(16);
                            dataSafeTecDesc.put("item", coVerification.getStandardProvision());
                            String desc = StrUtil.isNotEmpty(coVerification.getDescription()) ? coVerification.getDescription() : descMap.get(coVerification.getBpCode());
                            dataSafeTecDesc.put("desc", desc == null ? "暂无安全措施" : desc);
                            model.put("desc" + StrUtil.replace(coVerification.getBpCode(), ".", ""),
                                desc == null ? "暂无安全措施" : desc);
                            model.put("result" + StrUtil.replace(coVerification.getBpCode(), ".", ""),
                                ("不适用".equals(coVerification.getResult()) || "全部符合"
                                    .equals(coVerification.getResult())) ? "不存在" : "存在");
                            dataDisclosureDescList.add(dataSafeTecDesc);
                        });
                        model.put("dataDisclosureList", dataDisclosureList);
                        model.put("dataDisclosureDescList", dataDisclosureDescList);
                        break;
                    case "数据删除":
                        // 数据删除
                        List<Map<String, Object>> dataDeleteList = new ArrayList<>();
                        List<Map<String, Object>> dataDeleteDescList = new ArrayList<>();
                        v.stream().sorted(Comparator.comparing(CoVerification::getBpCode)).forEach(coVerification -> {
                            Map<String, Object> dataDelete = new HashMap<>(16);
                            dataDelete.put("code", coVerification.getBpCode());
                            dataDelete.put("content", coVerification.getStandardProvision());
                            dataDelete.put("result", ("不适用".equals(coVerification.getResult()) || "全部符合"
                                .equals(coVerification.getResult())) ? "不存在" : "存在");
                            dataDeleteList.add(dataDelete);

                            Map<String, Object> dataSafeTecDesc = new HashMap<>(16);
                            dataSafeTecDesc.put("item", coVerification.getStandardProvision());
                            String desc = StrUtil.isNotEmpty(coVerification.getDescription()) ? coVerification.getDescription() : descMap.get(coVerification.getBpCode());
                            dataSafeTecDesc.put("desc", desc == null ? "暂无安全措施" : desc);
                            model.put("desc" + StrUtil.replace(coVerification.getBpCode(), ".", ""),
                                desc == null ? "暂无安全措施" : desc);
                            model.put("result" + StrUtil.replace(coVerification.getBpCode(), ".", ""),
                                ("不适用".equals(coVerification.getResult()) || "全部符合"
                                    .equals(coVerification.getResult())) ? "不存在" : "存在");
                            dataDeleteDescList.add(dataSafeTecDesc);
                        });
                        model.put("dataDeleteList", dataDeleteList);
                        model.put("dataDeleteDescList", dataDeleteDescList);
                        break;
                    case "数据出境":
                        // 数据出境
                        List<Map<String, Object>> dataExportList = new ArrayList<>();
                        List<Map<String, Object>> dataExportDescList = new ArrayList<>();
                        v.stream().sorted(Comparator.comparing(CoVerification::getBpCode)).forEach(coVerification -> {
                            Map<String, Object> dataExport = new HashMap<>(16);
                            dataExport.put("code", coVerification.getBpCode());
                            dataExport.put("content", coVerification.getStandardProvision());
                            dataExport.put("result", ("不适用".equals(coVerification.getResult()) || "全部符合"
                                .equals(coVerification.getResult())) ? "不存在" : "存在");
                            dataExportList.add(dataExport);

                            Map<String, Object> dataSafeTecDesc = new HashMap<>(16);
                            dataSafeTecDesc.put("item", coVerification.getStandardProvision());
                            String desc = StrUtil.isNotEmpty(coVerification.getDescription()) ? coVerification.getDescription() : descMap.get(coVerification.getBpCode());
                            dataSafeTecDesc.put("desc", desc == null ? "暂无安全措施" : desc);
                            model.put("desc" + StrUtil.replace(coVerification.getBpCode(), ".", ""),
                                desc == null ? "暂无安全措施" : desc);
                            model.put("result" + StrUtil.replace(coVerification.getBpCode(), ".", ""),
                                ("不适用".equals(coVerification.getResult()) || "全部符合"
                                    .equals(coVerification.getResult())) ? "不存在" : "存在");
                            dataExportDescList.add(dataSafeTecDesc);
                        });
                        model.put("dataExportList", dataExportList);
                        model.put("dataExportDescList", dataExportDescList);
                        break;
                    default:
                        log.warn("无法识别{}", k);
                        break;
                }
            });
    }

    /**
     * 默认模板需排除的章节内容
     */
    @Override
    public Set<LabelEnum> getNeedExcludeContent() {
        return CollUtil.newHashSet(LabelEnum.SMZQ);
    }

    @Override
    public ReportTypeEnum getType() {
        return ReportTypeEnum.JIANGSU;
    }


    @Getter
    @Setter
    public static class RiskResult{
        private String busSystem;
        private String name;
        private String comment;
        private String threatType;
        private String process;
        private String level;
        private String t;
        private String v;
        private String b;
        private String l;
        private String f;
        private String rn;
        private String riskLevel;
    }
}
