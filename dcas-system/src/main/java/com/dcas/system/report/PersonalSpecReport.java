package com.dcas.system.report;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.domain.entity.*;
import com.dcas.common.enums.LabelEnum;
import com.dcas.common.enums.OptEnum;
import com.dcas.common.model.dto.*;
import com.dcas.common.model.excel.SpecPersonRiskExcel;
import com.dcas.common.model.other.PersonalInfoClassify;
import com.dcas.common.model.vo.FormConfigTreeVO;
import com.dcas.common.model.vo.QueryProjectOperationExportVo;
import com.dcas.common.mapper.*;
import com.dcas.system.service.IMidOperationService;
import com.dcas.system.service.IMidThreatResultService;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.deepoove.poi.plugin.toc.TOCRenderPolicy;
import com.deepoove.poi.util.PoitlIOUtils;
import com.deepoove.poi.xwpf.NiceXWPFDocument;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 个人信息专项评估报告
 * <AUTHOR>
 * @date 2024-04-29
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PersonalSpecReport extends AbstractReport implements ReportInterface{

    private final SpecialEvaluationConfigMapper specialEvaluationConfigMapper;
    private final CoGapAnalysisMapper coGapAnalysisMapper;
    private final CoVerificationMapper coVerificationMapper;
    private final DiscoveryClassifyDataMapper discoveryClassifyDataMapper;
    private final StandardItemMapper standardItemMapper;
    private final CoThreatAnalysisMapper coThreatAnalysisMapper;
    private final CoLegalMapper coLegalMapper;
    private final AdviseRiskMapper adviseRiskMapper;
    private final CoModelAnalysisResultMapper coModelAnalysisResultMapper;
    private final IMidThreatResultService iMidThreatResultService;
    private final DynamicProcessTreeMapper dynamicProcessTreeMapper;
    private final IMidOperationService iMidOperationService;

    @Value("${safety.profile}")
    private String basePath;

    @Override
    public String exportWord(ExportWordDto dto, QueryProjectOperationExportVo poVo) throws Exception {
        List<String> filePathList = new ArrayList<>();
        CompletableFuture<String> future = CompletableFuture.allOf(CompletableFuture.supplyAsync(() -> {
            try {
                return process(dto, poVo);
            } catch (IOException e) {
                log.error("导出专项报告失败", e);
                return null;
            }
        }).whenComplete((v, th) -> {
            if (th != null) {
                log.error("", th);
            }
            if (v != null) {
                log.info(v.toString());
                filePathList.addAll(v);
            }
        })).thenApply(v -> zip(filePathList, poVo.getOperationName(), basePath));
        return future.get();
    }

    @SchemaSwitch(value = ExportWordDto.class)
    @Override
    public List<String> process(ExportWordDto dto, QueryProjectOperationExportVo poVo) throws IOException {
        SpecialEvaluationConfig specialEvaluationConfig = specialEvaluationConfigMapper.selectById(poVo.getSpecId());
        if (specialEvaluationConfig.getReportPath() == null){
            return null;
        }
        List<String> fileNames = StrUtil.split(specialEvaluationConfig.getReportPath(),StrUtil.C_COMMA);
        List<String> filePathList = new ArrayList<>();
        fileNames.forEach(s -> {
            if (s.endsWith(".doc") || s.endsWith(".docx")){
                try {
                    filePathList.add(doProcessWord(dto, poVo, s));
                } catch (IOException e) {
                    log.error("处理word报告失败,{}",s, e);
                }
            } else if (s.endsWith(".xls") || s.endsWith(".xlsx")){
                try {
                    filePathList.add(doProcessExcel(poVo, s));
                } catch (IOException e) {
                    log.error("处理excel报告失败，{}",s, e);
                }
            } else {
                log.warn("不支持的文件类型,{}",s);
            }
        });
        return filePathList;
    }


    private String doProcessExcel( QueryProjectOperationExportVo poVo, String fileName)
        throws IOException {
        String filePath = StrUtil.join(File.separator, basePath,"template", fileName);
        String path = String.join(File.separator, basePath, "temp", "PIA安全风险综合分析结果清单.xlsx");
        String operationId = poVo.getOperationId();
        List<CoModelAnalysisResult> coModelAnalysisResultList = coModelAnalysisResultMapper
            .selectList(new QueryWrapper<CoModelAnalysisResult>().eq("operation_id", operationId));
        List<SpecPersonRiskExcel> resultList = getSpecPersonRiskExcel(coModelAnalysisResultList);
        EasyExcel.write(path).withTemplate(filePath).sheet().doWrite(resultList);
        return path;
    }

    private List<SpecPersonRiskExcel> getSpecPersonRiskExcel(List<CoModelAnalysisResult> coModelAnalysisResultList) {
        List<SpecPersonRiskExcel> resultList = new ArrayList<>();
        if (coModelAnalysisResultList != null){

            String titlesStr = "";
            List<Map> resultMapList = new ArrayList<>();
            for (CoModelAnalysisResult coModelAnalysisResult : coModelAnalysisResultList){
                titlesStr = coModelAnalysisResult.getTitles();
                List<Map> mapList = JSONUtil.toList(coModelAnalysisResult.getResult(), Map.class);
                resultMapList.addAll(mapList);
            }

            List<FormConfigTreeVO> titles = JSONUtil.toList(titlesStr, FormConfigTreeVO.class);
            Map<String, String> indexColumnMap = new HashMap<>(16);
            titles.forEach(formConfigTreeVO -> {
                if (formConfigTreeVO.getDataColumn()){
                    String title = formConfigTreeVO.getTitle().trim();
                    switch (title) {
                        case "资产详情":
                            indexColumnMap.put(formConfigTreeVO.getDataIndex(), "columns");
                            break;
                        case "风险等级":
                            indexColumnMap.put(formConfigTreeVO.getDataIndex(), "riskLevel");
                            break;
                        default:
                            break;
                    }
                } else {
                    if (CollUtil.isNotEmpty(formConfigTreeVO.getChildren())) {
                        formConfigTreeVO.getChildren().forEach(child -> {
                            if ("资产名称".equals(child.getTitle())) {
                                indexColumnMap.put(child.getDataIndex(), "assetName");
                            } else if ("敏感等级".equals(child.getTitle())) {
                                indexColumnMap.put(child.getDataIndex(), "sensitiveLevel");
                            } else if ("风险源维度".equals(child.getTitle())) {
                                indexColumnMap.put(child.getDataIndex(), "dimension");
                            } else if ("安全事件发生的可能性等级".equals(child.getTitle())) {
                                indexColumnMap.put(child.getDataIndex(), "possibility");
                            } else if ("对个人权益产生的影响类别".equals(child.getTitle())) {
                                indexColumnMap.put(child.getDataIndex(), "classify");
                            } else if ("影响程度".equals(child.getTitle())) {
                                indexColumnMap.put(child.getDataIndex(), "impact");
                            }
                        });
                    } else {
                        if ("所属业务系统".equals(formConfigTreeVO.getTitle())) {
                            indexColumnMap.put(formConfigTreeVO.getDataIndex(), "busSystemName");
                        }
                    }
                }
            });

            AtomicInteger sort= new AtomicInteger(1);
            resultMapList.forEach(map -> {
                SpecPersonRiskExcel specPersonRiskExcel = new SpecPersonRiskExcel();
                for (Object o : map.keySet()) {
                    String key = (String)o;
                    if (indexColumnMap.get(key) == null){
                        continue;
                    }
                    String column = indexColumnMap.get(key);
                    switch (column){
                        case "busSystemName":
                            specPersonRiskExcel.setBusSystem((String)map.get(key));
                            break;
                        case "assetName":
                            specPersonRiskExcel.setAssetName((String)map.get(key));
                            break;
                        case "columns":
                            specPersonRiskExcel.setColumns((String)map.get(key));
                            break;
                        case "riskLevel":
                            specPersonRiskExcel.setRiskLevel((String)map.get(key));
                            break;
                        case "dimension":
                            specPersonRiskExcel.setDimension((String)map.get(key));
                            break;
                        case "possibility":
                            specPersonRiskExcel.setPossibility((String)map.get(key));
                            break;
                        case "classify":
                            specPersonRiskExcel.setClassify((String)map.get(key));
                            break;
                        case "impact":
                            specPersonRiskExcel.setImpact((String)map.get(key));
                            break;
                        default:
                            break;
                    }
                }
                specPersonRiskExcel.setSort(sort.getAndIncrement());
                resultList.add(specPersonRiskExcel);
            });
        }
        return resultList;
    }

    private String doProcessWord(ExportWordDto dto, QueryProjectOperationExportVo poVo, String fileName)
        throws IOException {
        String filePath = StrUtil.join(File.separator, basePath,"template", fileName);
        InputStream inputStream = Files.newInputStream(Paths.get(filePath));

        //数据模型
        Map<String, Object> model = new HashMap<>(16);

        //准备数据
        String operationId = dto.getOperationId();
        List<ExportWordChart> chartList = dto.getChartList();

        // 报告公共部分
        putCommonInfo(poVo, model);
        // 处理截图
//        processChartList(model, chartList, operationId);

        //3 数据映射分析
        //3.1个人信息识别 5 个人权益影响分析
        processPersonInfo(operationId, model);

        // 4.2威胁识别
        List<CoThreatAnalysis> coThreatAnalyses =
            coThreatAnalysisMapper.selectList(new QueryWrapper<CoThreatAnalysis>().eq("operation_id", operationId));
        List<TreeLabelDTO> treeLabelList = dynamicProcessTreeMapper.selectByOperationIdAndTreeId(operationId, LabelEnum.XTDY.getCode());
        Map<String, Long> busSystemMap = treeLabelList.stream().collect(Collectors.toMap(TreeLabelDTO::getTreeName,TreeLabelDTO::getTreeId));
        List<MidThreatResult> midThreatResultList = iMidThreatResultService.lambdaQuery().eq(MidThreatResult::getOperationId, operationId).list();
        Map<Long, List<MidThreatResult>> threatMap = midThreatResultList.stream().collect(Collectors.groupingBy(MidThreatResult::getSystemId));
        if (CollUtil.isNotEmpty(coThreatAnalyses)){
            List<Map<String, Object>> threatList = new ArrayList<>();
            coThreatAnalyses.forEach(coThreatAnalysis -> {
                Map<String, Object> map = new HashMap<>(16);
                map.put("busSystem", coThreatAnalysis.getBusSystem());
                map.put("T1level", "不涉及");
                map.put("T2level", "不涉及");
                map.put("T3level", "不涉及");
                map.put("T4level", "不涉及");
                map.put("T5level", "不涉及");
                map.put("T6level", "不涉及");
                map.put("T7level", "不涉及");
                map.put("T8level", "不涉及");
                map.put("T9level", "不涉及");
                List<MidThreatResult> threatResultList = threatMap.get(busSystemMap.get(coThreatAnalysis.getBusSystem()));
                threatResultList.forEach(midThreatResult -> map.put(midThreatResult.getThreatChild()+"level", midThreatResult.getThreatFrequencyTag() ));
                threatList.add(map);
            });
            model.put("threatList", threatList);
        }

        // 4 风险源识别
        // 4.1脆弱性识别 
        // 4.4安全事件发生的可能性
        vulIdentification(operationId, model, coThreatAnalyses, busSystemMap, threatMap);

        // 4.3合规性识别
        lawIdentification(operationId, model);
        // 5.3个人信息处理活动问题识别
        personalProblem(operationId, model);
        // 6 安全风险综合分析
        riskAnalysis(operationId, model);
        // 7 处置建议
        processSuggestions(operationId, model);

        //配置模板
        LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
        TOCRenderPolicy tocPolicy = new TOCRenderPolicy();
        Configure config =
            Configure.builder()
                .bind("dataSaftyHighSuggests", policy).bind("dataTecHighSuggests", policy)
                .bind("dataSaftyMidSuggests", policy).bind("dataTecMidSuggests", policy)
                .bind("lawIdentificationList", policy)
                .bind("vulIdentificationList", policy)
                .bind("personInfoList", policy)
                .bind("safetyEventList", policy)
                .bind("personalSafetyImpactList", policy)
                .bind("riskAnalysisList", policy)
                .bind("detailRiskAnalysisList", policy)
                .bind("impactIdentificationList", policy)
                .bind("problemList", policy)
                .bind("impactList", policy)
                .bind("threatList", policy)
                .bind("tocContents", tocPolicy).build();

        //加载模板渲染数据
        XWPFTemplate template = XWPFTemplate.compile(inputStream, config).render(model);
        NiceXWPFDocument xwpfDocument = template.getXWPFDocument();
        xwpfDocument.enforceUpdateFields();

        String realFileName = String.format("%s专项报告.docx", poVo.getOperationName());
        String path = String.join(File.separator, basePath, "temp", realFileName);
        FileUtil.touch(path);

        //输出结果
        OutputStream out = Files.newOutputStream(Paths.get(path));
        BufferedOutputStream bos = new BufferedOutputStream(out);
        xwpfDocument.write(bos);
        bos.flush();
        out.flush();
        PoitlIOUtils.closeQuietlyMulti(template, xwpfDocument, bos, out);
        return path;
    }

    @SchemaSwitch(String.class)
    private void riskAnalysis(String operationId, Map<String, Object> model) {
        List<Map<String, Object>> riskResultList = new ArrayList<>();
        List<Map<String, Object>> detailRiskAnalysisList = new ArrayList<>();
        List<CoModelAnalysisResult> coModelAnalysisResultList = coModelAnalysisResultMapper
            .selectList(new QueryWrapper<CoModelAnalysisResult>().eq("operation_id", operationId));

        String titlesStr = "";
        List<Map> resultList = new ArrayList<>();
        for (CoModelAnalysisResult coModelAnalysisResult : coModelAnalysisResultList){
            titlesStr = coModelAnalysisResult.getTitles();
            List<Map> mapList = JSONUtil.toList(coModelAnalysisResult.getResult(), Map.class);
            resultList.addAll(mapList);
        }
        List<FormConfigTreeVO> titles = JSONUtil.toList(titlesStr, FormConfigTreeVO.class);
        Map<String, String> indexColumnMap = new HashMap<>(16);
        titles.forEach(formConfigTreeVO -> {
            if (formConfigTreeVO.getDataColumn()){
                String title = formConfigTreeVO.getTitle().trim();
                switch (title) {
                    case "资产详情":
                        indexColumnMap.put(formConfigTreeVO.getDataIndex(), "columns");
                        break;
                    case "风险等级":
                        indexColumnMap.put(formConfigTreeVO.getDataIndex(), "riskLevel");
                        break;
                    default:
                        break;
                }
            } else {
                if (CollUtil.isNotEmpty(formConfigTreeVO.getChildren())) {
                    formConfigTreeVO.getChildren().forEach(child -> {
                        if ("资产名称".equals(child.getTitle())) {
                            indexColumnMap.put(child.getDataIndex(), "assetName");
                        } else if ("敏感等级".equals(child.getTitle())) {
                            indexColumnMap.put(child.getDataIndex(), "sensitiveLevel");
                        } else if ("风险源维度".equals(child.getTitle())) {
                            indexColumnMap.put(child.getDataIndex(), "dimension");
                        } else if ("安全事件发生的可能性等级".equals(child.getTitle())) {
                            indexColumnMap.put(child.getDataIndex(), "possibility");
                        } else if ("对个人权益产生的影响类别".equals(child.getTitle())) {
                            indexColumnMap.put(child.getDataIndex(), "classify");
                        } else if ("影响程度".equals(child.getTitle())) {
                            indexColumnMap.put(child.getDataIndex(), "impact");
                        }
                    });
                } else {
                    if ("所属业务系统".equals(formConfigTreeVO.getTitle())) {
                        indexColumnMap.put(formConfigTreeVO.getDataIndex(), "busSystemName");
                    }
                }
            }
        });

        AtomicInteger sort= new AtomicInteger(1);
        resultList.forEach(map -> {
            Map<String, Object> resultMap = new HashMap<>(16);
            for (Object o : map.keySet()) {
                String key = (String)o;
                if (indexColumnMap.get(key) == null){
                    continue;
                }
                String column = indexColumnMap.get(key);
                // 敏感程度
                if ("sensitiveLevel".equals(column)){
                    resultMap.put(column, convertSensitiveLevel(map.get(key)));
                } else {
                    resultMap.put(column, map.get(key));
                }
            }
            resultMap.put("sort", sort.getAndIncrement());
            riskResultList.add(resultMap);
        });

    // 截取前100
    List<Map<String, Object>> result = new ArrayList<>();
    result = riskResultList.stream().limit(100).collect(Collectors.toList());

    model.put("impactList", result);
    model.put("riskAnalysisList", result);
    List<AdviseRiskDTO> adviseRisks = adviseRiskMapper.selectRiskContentWithLawIds(operationId);
    if (CollUtil.isNotEmpty(adviseRisks)) {
        List<String> itemIds = adviseRisks.stream().map(AdviseRiskDTO::getItemId).collect(
            Collectors.toList());
        // 获取合规依据
        List<AdviseRiskDTO> lawBasics = adviseRiskMapper.selectLawBasicByItemIds(itemIds, operationId);
        // 获取能力依据
        List<AdviseRiskDTO> analysisBasics = adviseRiskMapper.selectAnalysisBasicByItemIds(itemIds, operationId);
        List<AdviseRiskDTO> allBasics = new ArrayList<>();
        allBasics.addAll(lawBasics);
        allBasics.addAll(analysisBasics);
        Map<String, String> itemBasicMap = allBasics.stream().collect(Collectors.groupingBy(AdviseRiskDTO::getItemId, Collectors.mapping(AdviseRiskDTO::getBasis, Collectors.joining(", "))));
        AtomicInteger adviseSort = new AtomicInteger(1);
        adviseRisks.stream().filter(adviseRiskDTO -> adviseRiskDTO.getContent() != null)
            .collect(Collectors.groupingBy(AdviseRiskDTO::getContent)).forEach((k, v) -> {
                Map<String, Object> map = new HashMap<>(16);
                Set<String> set = new HashSet<>();
                StringBuilder sb = new StringBuilder();
                for (AdviseRiskDTO adviseRiskDTO : v) {
                    String basis = itemBasicMap.get(adviseRiskDTO.getItemId());
                    if (StrUtil.isEmpty(basis)) {
                        continue;
                    }
                    basis = basis.replace(" ", "");
                    if (basis.contains("，")) {
                        set.addAll(Arrays.asList(basis.split("，")));
                    } else if (basis.contains(StrPool.COMMA)) {
                        set.addAll(Arrays.asList(basis.split(StrPool.COMMA)));
                    } else {
                        set.add(basis);
                    }
                    sb.append(CharSequenceUtil.addSuffixIfNot(adviseRiskDTO.getDescribe(), ";"));
                }
                if (!set.isEmpty()) {
                    map.put("sort", adviseSort.getAndIncrement());
                    map.put("content", k);
                    String basis = CollUtil.join(set, StrPool.LF);
                    // 改为风险项
                    map.put("basis", sb.toString());
                    detailRiskAnalysisList.add(map);
                }
            });
        }
        model.put("detailRiskAnalysisList", detailRiskAnalysisList);

    }

    private String convertSensitiveLevel(Object o) {
        if (ObjectUtil.equals(o,1)){
            return "不敏感";
        } else if (ObjectUtil.equals(o,2)) {
            return "低敏感";
        } else if (ObjectUtil.equals(o,3)){
            return "较敏感";
        } else {
            return "敏感";
        }
    }

    private void lawIdentification(String operationId, Map<String, Object> model) {
        List<Map<String, Object>> lawIdentificationList = new ArrayList<>();
        List<CoLegal> coLegalList = coLegalMapper.selectList(
            new QueryWrapper<CoLegal>().eq("operation_id", operationId).ne("result", OptEnum.D.getInfo()));
        if (CollUtil.isNotEmpty(coLegalList)){
//            Map<String, String> descMap = getLawDescribe(operationId);
            AtomicInteger sort = new AtomicInteger(1);
            lawIdentificationList = coLegalList.stream().filter(law-> "个人信息保护法".equals(law.getLawName())).sorted(Comparator.comparing(CoLegal::getItemNum)).map(coLegal -> {
                Map<String, Object> map = new HashMap<>(16);
                map.put("sort", sort.getAndIncrement());
                map.put("content", coLegal.getItemContent());
                map.put("desc", coLegal.getDesc());
                map.put("result", coLegal.getResult());
                return map;
            }).collect(Collectors.toList());
        }
        model.put("lawIdentificationList", lawIdentificationList);
    }

    @SchemaSwitch(String.class)
    private void vulIdentification(String operationId, Map<String, Object> model,
        List<CoThreatAnalysis> coThreatAnalyses, Map<String, Long> busSystemIdMap,
        Map<Long, List<MidThreatResult>> midThreatMap) {
        List<StandardItem> standardItems = standardItemMapper.queryAllItemsByStandardId(1024);
        QueryWrapper<CoVerification> query = new QueryWrapper<>();
        query.eq("operation_id", operationId);
        query.eq("standard_id", 1024);
        List<CoVerification> coVerifications = coVerificationMapper.selectList(query);
        List<Map<String, Object>> list = new ArrayList<>();
        List<Map<String, Object>> busSystemlist = new ArrayList<>();
        List<String> riskLevelCountList = new ArrayList<>();
        List<String> stageSort = Lists.newArrayList("网络环境和技术措施", "个人信息处理流程", "参与人员与第三方", "业务特点、规模及安全态势");
        if (CollUtil.isNotEmpty(coVerifications)) {
            List<MidOperation> midOperationList = iMidOperationService.lambdaQuery().eq(MidOperation::getOperationId, operationId).list();
            Map<String, MidOperation> buySystemOperationMap = midOperationList.stream().collect(Collectors.toMap(MidOperation::getBusSystem, Function.identity(), (k1,k2)->k1));
            Map<String, String> threatMap = calcThreatMap(coThreatAnalyses, busSystemIdMap, midThreatMap, buySystemOperationMap);

            List<CoModelAnalysisResult> coModelAnalysisResultList = coModelAnalysisResultMapper.selectList(
                new QueryWrapper<CoModelAnalysisResult>().eq("operation_id", operationId));
            List<SpecPersonRiskExcel> specPersonRiskExcels = getSpecPersonRiskExcel(coModelAnalysisResultList);
            Map<String, List<SpecPersonRiskExcel>> riskMap = specPersonRiskExcels.stream().collect(Collectors.groupingBy(
                specPersonRiskExcel -> StrUtil.join(StrUtil.DOT, specPersonRiskExcel.getBusSystem(),
                    specPersonRiskExcel.getDimension())));
//            Map<String, String> descMap = getDescribe(operationId);
            Map<String, CoVerification> bpCodeMap =
                coVerifications.stream().collect(Collectors.toMap(CoVerification::getBpCode, Function.identity()));
            threatMap.forEach((busSystem,threat)->{
                Map<String, Object> busSystemMap = new HashMap<String, Object>();
                List<Map<String, Object>> busList = new ArrayList<>();
                busSystemMap.put("busSystem", busSystem);
                standardItems.stream().collect(Collectors.groupingBy(StandardItem::getStage)).forEach((k, v) -> {
                    v = v.stream().sorted(Comparator.comparingDouble(StandardItem :: getBpCodeDouble)).collect(
                        Collectors.toList());
                    v.forEach(standardItem -> {
                        if (standardItem == null){
                            return;
                        }
                        CoVerification coVerification = bpCodeMap.get(standardItem.getBpCode());
                        if (coVerification == null){
                            return;
                        }

                        Map<String, Object> map = new HashMap<>(16);
                        map.put("stage", standardItem.getStage());
                        map.put("content", standardItem.getContent());
                        map.put("measure",
                            !OptEnum.C.getInfo().equals(coVerification.getResult()) && !OptEnum.D.getInfo()
                                .equals(coVerification.getResult()) ? coVerification.getDescription() : "");
                        map.put("result", coVerification.getResult());
                        map.put("level", convertLevel(coVerification.getResult()));
                        map.put("threat", threat);
                        String key = StrUtil.join(StrUtil.DOT, busSystem, standardItem.getStage());
                        if (CollUtil.isNotEmpty(riskMap.get(key))) {
                            map.put("possibility",
                                riskMap.get(key).stream().map(SpecPersonRiskExcel::getPossibility).distinct()
                                    .collect(Collectors.joining(StrPool.LF)));
                        }
                        riskLevelCountList.add((String)map.get("level"));
                        busList.add(map);
                    });
                });
                // 按照风险源维度排序
                AtomicInteger sort = new AtomicInteger(1);
                List<Map<String, Object>> sortList = new ArrayList<>();
                    busList.stream().sorted(Comparator.comparing(c -> stageSort.indexOf(c.get("stage")))).collect(Collectors.toList()).forEach(map ->
                    {
                        map.put("sort", sort.getAndIncrement());
                        sortList.add(map);
                    });
                busSystemMap.put("safetyEventList", sortList);
                busSystemlist.add(busSystemMap);
                list.addAll(busList);
            });
        }
        // 按照风险源维度排序
        AtomicInteger sort = new AtomicInteger(1);
        List<Map<String, Object>> sortList = new ArrayList<>();
        list.stream().sorted(Comparator.comparing(c -> stageSort.indexOf(c.get("stage")))).collect(Collectors.toList()).forEach(map->
        {
            map.put("sort", sort.getAndIncrement());
            sortList.add(map);
        });
        model.put("vulIdentificationList", sortList);
        model.put("busSystemList", busSystemlist);
        riskLevelCountList.stream().collect(Collectors.groupingBy(s -> s)).forEach((k,v)->{
            if ("很高".equals(k)){
                model.put("highestCount", v.size());
            } else if ("高".equals(k)){
                model.put("highCount", v.size());
            } else if ("中".equals(k)){
                model.put("midCount", v.size());
            } else if ("低".equals(k)){
                model.put("lowCount", v.size());
            }
        });
    }

    private Map<String, String> calcThreatMap(List<CoThreatAnalysis> coThreatAnalyses, Map<String, Long> busSystemIdMap,
        Map<Long, List<MidThreatResult>> midThreatMap, Map<String, MidOperation> busSystemOperationMap) {
        Map<String, String> result = new HashMap<>(16);
        // 计算公式：SUM(选中的威胁分类等级之和)/CT(选中的威胁分类数量之和)*最大威胁等级
        coThreatAnalyses.forEach(coThreatAnalysis -> {
            Long busSystemId = busSystemIdMap.get(coThreatAnalysis.getBusSystem());
            MidOperation midOperation = busSystemOperationMap.get(coThreatAnalysis.getBusSystem());
            List<MidThreatResult> midThreatResults = midThreatMap.get(busSystemId);
            double sumLevel = midThreatResults.stream().mapToDouble(midThreatResult -> midThreatResult.getThreatFrequencyLevel().doubleValue()).sum();
            Integer frequency = (int)Math.ceil(sumLevel/midThreatResults.size()*midOperation.getMaxThreatLevel());
            result.put(coThreatAnalysis.getBusSystem(), convertThreat(frequency));
        });
        return result;
    }

    private void personalProblem(String operationId, Map<String, Object> model) {
        List<Map<String, Object>> list = new ArrayList<>();
        QueryWrapper<CoVerification> query = new QueryWrapper<>();
        query.eq("operation_id", operationId);
        query.eq("standard_id", 1001);
        query.ne("result", OptEnum.D.getInfo());
        query.ne("result", OptEnum.A.getInfo());
        List<CoVerification> coVerifications = coVerificationMapper.selectList(query);
        if (CollUtil.isNotEmpty(coVerifications)){

            AtomicInteger sort = new AtomicInteger(1);
            Map<String, String> descMap = getDescribe(operationId);
            coVerifications.stream().sorted(Comparator.comparingInt(
                    s -> Integer.parseInt(CharSequenceUtil.replace(s.getBpCode(), StrPool.DOT, CharSequenceUtil.EMPTY))))
                .forEach(coVerification -> {
                    Map<String, Object> map = new HashMap<>(16);
                    map.put("sort", sort.getAndIncrement());
                    map.put("itemTitle", coVerification.getProcess());
                    map.put("itemContent", coVerification.getStandardProvision());
                    String desc = descMap.get(coVerification.getBpCode());
                    map.put("desc", desc != null ? desc : "未识别相关问题");
                    list.add(map);
                });
        }
        model.put("problemList", list);
    }

    private Map<String, String> getDescribe(String operationId) {
        List<QuestionnaireResultDTO> questionnaireResultList =
            coVerificationMapper.getBpCodeRiskDesc(operationId);
        Map<String, String> bpCodeMap = new HashMap<>(16);
        if (CollUtil.isNotEmpty(questionnaireResultList)) {
            Map<String, List<QuestionnaireResultDTO>> map =
                questionnaireResultList.stream().collect(Collectors.groupingBy(QuestionnaireResultDTO::getBpCode));
            map.forEach((k, v) -> {
                Set<String> duplicateSet = new HashSet<>();
                StringBuilder desc = new StringBuilder();
                for (QuestionnaireResultDTO questionnaireResultDTO : v) {
                    if (!duplicateSet.contains(questionnaireResultDTO.getContent()) && StrUtil.isNotEmpty(questionnaireResultDTO.getContent())) {
                        if (CharSequenceUtil.isEmpty(questionnaireResultDTO.getExplain())) {
                            desc.append(CharSequenceUtil.addSuffixIfNot(questionnaireResultDTO.getContent(), ";"));
                        }
                        duplicateSet.add(questionnaireResultDTO.getItem());
                    }
                }
                bpCodeMap.put(k, desc.toString());
            });
        }
        return bpCodeMap;
    }

    private String convertLevel(String result) {
        switch (result){
            case "不符合":
                return "很高";
            case "大部分符合":
                return "高";
            case "部分符合":
                return "中";
            case "完全符合":
            default:
                return "低";
        }
    }

    private String convertThreat(Integer frequency) {
        switch (frequency) {
            case 1:
                return "低";
            case 2:
                return "中";
            case 3:
                return "高";
            case 4:
            default:
                return "很高";
        }
    }

    private void processPersonInfo(String operationId, Map<String, Object> model) {
        List<PersonalInfoClassify> personalInfoClassifyList = discoveryClassifyDataMapper.selectPersonInfoClassify(operationId);
        List<Map<String, Object>> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(personalInfoClassifyList)){
            AtomicInteger st = new AtomicInteger(1);
            personalInfoClassifyList.stream().collect(Collectors.groupingBy(PersonalInfoClassify::getKey)).forEach((k,v) -> {
                boolean first = true;
                Map<String, Object> map = new HashMap<>(16);
                Set<String> set = new HashSet<>();
                Set<String> busSystemSet = new HashSet<>();
                for (PersonalInfoClassify personalInfoClassify : v){
                    if (first){
                        map.put("sort", st.getAndIncrement());
                        map.put("busName", personalInfoClassify.getBusName());
                        map.put("type", personalInfoClassify.getType());
                        map.put("subType", personalInfoClassify.getSubType());
                        map.put("safetyLevel", personalInfoClassify.getSafetyLevel());
                        map.put("sensitiveLevel", personalInfoClassify.getSensitiveLevel());
                        first = false;
                    }
                    if (personalInfoClassify.getClassify() != null){
                        set.add(personalInfoClassify.getClassify());
                    }
                    if (personalInfoClassify.getBusSystemName() != null) {
                        busSystemSet.add(personalInfoClassify.getBusSystemName());
                    }
                }
                map.put("classify", set.isEmpty() ? "" :  CollUtil.join(set, StrPool.LF));
                map.put("busSystemName", CollUtil.join(busSystemSet, StrPool.LF));
                list.add(map);
            });
        }
        model.put("personInfoList", list);
        model.put("personalSafetyImpactList", list);
        model.put("impactIdentificationList", list);
    }


    private void processChartList(Map<String, Object> model, List<ExportWordChart> chartList, String operationId) {
        List<Map<String, Object>> analysisContents = new ArrayList<>();
        String name = coGapAnalysisMapper.queryFileName(operationId);
        for (ExportWordChart chart : chartList) {
            if (chart.getName().equals("specOverviewImg")){
                Map<String, String> imageMap = getPicture(Lists.newArrayList(chart));
                model.put("specOverviewImg", getPictureStream(imageMap));
            }
            if (chart.getName().equals("lifecycleResultImg")){
                Map<String, String> imageMap = getPicture(Lists.newArrayList(chart));
                model.put("lifecycleResultImg", getPictureStream(imageMap));
            }
            if (chart.getName().equals("specRiskImg")){
                Map<String, String> imageMap = getPicture(Lists.newArrayList(chart));
                model.put("specRiskImg", getPictureStream(imageMap));
            }
            if (!chart.getName().startsWith(name)) {
                continue;
            }
            Map<String, Object> content = new HashMap<>();
            content.put("chartName", chart.getName());
            List<Map<String, Object>> chartImages = new ArrayList<>();
            Map<String, Object> chartMap = new HashMap<>();

            Map<String, String> imageMap = getPicture(Lists.newArrayList(chart));

            chartMap.put("chartImage", getPictureStream(imageMap));
            chartMap.put("chartImageName", chart.getName());
            chartImages.add(chartMap);
            content.put("chartImages", chartImages);
            analysisContents.add(content);
        }
        model.put("analysisContents", analysisContents);
    }

    @Override
    public ReportTypeEnum getType() {
        return ReportTypeEnum.PERSONAL_SPEC;
    }
}
