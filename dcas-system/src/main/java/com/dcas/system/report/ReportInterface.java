package com.dcas.system.report;

import com.dcas.common.enums.LabelEnum;
import com.dcas.common.model.dto.ExportWordDto;
import com.dcas.common.model.vo.QueryProjectOperationExportVo;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Set;

/**
 * 报告接口类
 *
 * <AUTHOR>
 * @date 2024/01/08 15:57
 **/
public interface ReportInterface {

    /**
     * 导出word报告
     * @param dto
     * @param poVo
     * @param modelId
     * @throws IOException
     */
    String exportWord(ExportWordDto dto, QueryProjectOperationExportVo poVo, Long modelId) throws Exception;

    /**
     * 导出专项评估报告
     * @param dto
     * @param poVo
     * @throws IOException
     */
    String exportWord(ExportWordDto dto, QueryProjectOperationExportVo poVo) throws Exception;


    /**
     * 导出word报告
     * @param response
     * @param dto
     * @throws IOException
     */
    void exportWord(HttpServletResponse response, ExportWordDto dto) throws IOException;

    /**
     * 报告处理，返回文件路径
     * @param dto
     * @param poVo
     * @param modelId
     * @return
     * @throws Exception
     */
    String process(ExportWordDto dto, QueryProjectOperationExportVo poVo, Long modelId) throws Exception;

    /**
     * 报告处理，返回文件路径
     * @param dto
     * @param poVo
     * @return
     * @throws Exception
     */
    List<String> process(ExportWordDto dto, QueryProjectOperationExportVo poVo) throws Exception;


    /**
     * 报告类型
     * @return {@link ReportTypeEnum}
     */
    ReportTypeEnum getType();

    /**
     * 默认模板需排除的章节内容
     */
    Set<LabelEnum> getNeedExcludeContent();
}
