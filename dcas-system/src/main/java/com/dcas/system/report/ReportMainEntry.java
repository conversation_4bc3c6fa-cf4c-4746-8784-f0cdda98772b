package com.dcas.system.report;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.domain.entity.SpecialEvaluationConfig;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.mapper.SpecialEvaluationConfigMapper;
import com.dcas.common.utils.spring.SpringUtils;
import com.dcas.common.model.dto.ExportWordDto;
import com.dcas.common.domain.entity.ModelFile;
import com.dcas.common.domain.entity.OperationModel;
import com.dcas.common.model.vo.QueryProjectOperationExportVo;
import com.dcas.common.mapper.CoOperationMapper;
import com.dcas.common.mapper.ModelFileMapper;
import com.dcas.common.mapper.OperationModelMapper;
import com.dcas.system.report.attachment.AttachmentReportFactory;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;

/**
 * 报告主入口
 *
 * <AUTHOR>
 * @date 2024/01/09 15:27
 **/
@RequiredArgsConstructor
@Component
@Getter
@Slf4j
public class ReportMainEntry {

    private final OperationModelMapper operationModelMapper;
    private final CoOperationMapper coOperationMapper;
    private final SpecialEvaluationConfigMapper specialEvaluationConfigMapper;

    @Value("${safety.profile:/dcas/storage}")
    private String basePath;

    @SchemaSwitch(ExportWordDto.class)
    public void entry(HttpServletResponse response, ExportWordDto dto, Long specId, ReportGroupEnum groupEnum) throws Exception {
        OperationModel operationModel =
            operationModelMapper.selectOne(new QueryWrapper<OperationModel>().eq("operation_id", dto.getOperationId()));
        String filePath = null;
        ModelFileMapper modelFileMapper = SpringUtils.getBean(ModelFileMapper.class);
        ModelFile modelFile = null;
        if (operationModel != null){
            modelFile = modelFileMapper.selectOne(new QueryWrapper<ModelFile>().eq("model_id", operationModel.getModelId()));
        }

        switch (groupEnum){
            case SPEC_WORD:
                //1、2阶段查询
                QueryProjectOperationExportVo operationVo = coOperationMapper.queryOperationExport(dto.getOperationId());
//                SpecialEvaluationConfig specialEvaluationConfig = specialEvaluationConfigMapper.selectById(specId);

                if (modelFile == null ){
                    // DSMM专项评估没有风险模板
                    filePath =
                        ReportFactory.getReportHandler(ReportTypeEnum.SPEC).exportWord(dto, operationVo);
                } else {
                    // PIA个人信息专项评估有风险模板，但报告配置在专项管理中
                    filePath =
                        ReportFactory.getReportHandler(ReportTypeEnum.getReportTypeByKey(modelFile.getFileType()))
                            .exportWord(dto, operationVo);
                }
                ReportUtil.output(response, filePath);
                break;
            case ASSESSMENT_WORD:
                //1、2阶段查询
                QueryProjectOperationExportVo poVo = coOperationMapper.queryOperationExport(dto.getOperationId());

                // 作业无风险评估情况
                if (operationModel == null) {
                    if ("汽车".equals(poVo.getRelatedIndustry())){
                        filePath = ReportFactory.getReportHandler(ReportTypeEnum.IOV).exportWord(dto, poVo);
                    } else {
                        filePath = ReportFactory.getReportHandler(ReportTypeEnum.DEFAULT).exportWord(dto, poVo, null);
                    }
                } else {
                    if (modelFile == null) {
                        throw new ServiceException("风险模型关联模板文件未配置");
                    }
                    // 作业有风险评估情况且选择的风险模型配置了通用报告规范
                    filePath = ReportFactory.getReportHandler(ReportTypeEnum.getReportTypeByKey(modelFile.getFileType()))
                        .exportWord(dto, poVo, operationModel.getModelId());
                }
                ReportUtil.output(response, filePath);
                break;
            case ASSET_LIST:
                //1、2阶段查询
                QueryProjectOperationExportVo vo = coOperationMapper.queryOperationExport(dto.getOperationId());
                // 作业无风险评估情况
                if (operationModel == null) {
                    AttachmentReportFactory.getAssetReportHandler(ReportTypeEnum.COMMON).exportWord(response, dto, vo);
                } else {
                    if (modelFile == null) {
                        throw new ServiceException("风险模型关联模板文件未配置");
                    }
                    log.info("file type is {}", modelFile.getFileType());
                    // 作业有风险评估情况且选择的风险模型配置了通用报告规范
                    try {
                        AttachmentReportFactory.getAssetReportHandler(ReportTypeEnum.getReportTypeByKey(modelFile.getFileType()))
                            .exportWord(response, dto, vo);
                    } catch (Exception e){
                        log.error("找不到对应的资产清单附件，使用通用模板", e);
                        AttachmentReportFactory.getAssetReportHandler(ReportTypeEnum.COMMON)
                            .exportWord(response, dto, vo);
                    }
                }
                break;
            default:
                log.warn("{} not supported!", groupEnum);
                break;
        }
    }

}
