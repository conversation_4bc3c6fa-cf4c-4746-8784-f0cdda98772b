package com.dcas.system.report;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.enums.LevelEnum;
import com.dcas.common.model.dto.ExportWordChart;
import com.dcas.common.model.dto.ExportWordDto;
import com.dcas.common.model.dto.ItemFileMapDTO;
import com.dcas.common.model.dto.QueryVerificationDetailDTO;
import com.dcas.common.domain.entity.*;
import com.dcas.common.model.vo.AnalysisStandardItemVO;
import com.dcas.common.model.vo.AnalysisStandardVO;
import com.dcas.common.model.vo.QueryProjectOperationExportVo;
import com.dcas.common.model.vo.SpecOverviewVO;
import com.dcas.common.mapper.*;
import com.dcas.system.service.CoViewCustomerService;
import com.dcas.system.service.SpecCalcResultService;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.deepoove.poi.plugin.toc.TOCRenderPolicy;
import com.deepoove.poi.util.PoitlIOUtils;
import com.deepoove.poi.xwpf.NiceXWPFDocument;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.*;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 专项评估报告
 * <AUTHOR>
 * @date 2024-04-08
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SpecReport extends AbstractReport implements ReportInterface{

    private final SpecialEvaluationConfigMapper specialEvaluationConfigMapper;
    private final CoGapAnalysisMapper coGapAnalysisMapper;
    private final CoVerificationMapper coVerificationMapper;
    private final CoViewCustomerService coViewCustomerService;
    private final AnalysisTemplateMapper analysisTemplateMapper;
    private final QuestionnaireContentMapper questionnaireContentMapper;
    private final SpecCalcResultService specCalcResultService;
    private final CoFileMapper coFileMapper;

    @Value("${safety.profile}")
    private String basePath;

    @Override
    public String exportWord(ExportWordDto dto, QueryProjectOperationExportVo poVo) throws Exception {
        List<String> filePathList = new ArrayList<>();
        CompletableFuture<String> future = CompletableFuture.allOf(CompletableFuture.supplyAsync(() -> {
            try {
                return process(dto, poVo);
            } catch (IOException e) {
                log.error("导出专项报告失败", e);
                return null;
            }
        }).whenComplete((v, th) -> {
            if (th != null) {
                log.error("", th);
            }
            if (v != null) {
                log.info(v.toString());
                filePathList.addAll(v);
            }
        })).thenApply(v -> zip(filePathList, poVo.getOperationName(), basePath));
        return future.get();
    }

    @SchemaSwitch(value = ExportWordDto.class)
    @Override
    public List<String> process(ExportWordDto dto, QueryProjectOperationExportVo poVo) throws IOException {
        SpecialEvaluationConfig specialEvaluationConfig = specialEvaluationConfigMapper.selectById(poVo.getSpecId());
        if (specialEvaluationConfig.getReportPath() == null){
            return null;
        }
        List<String> fileNames = StrUtil.split(specialEvaluationConfig.getReportPath(),StrUtil.C_COMMA);
        List<String> filePathList = new ArrayList<>();
        fileNames.forEach(s -> {
            if (s.endsWith(".doc") || s.endsWith(".docx")){
                try {
                    filePathList.add(doProcessWord(dto, poVo, s));
                } catch (IOException e) {
                    log.error("处理word报告失败,{}",s, e);
                }
            } else if (s.endsWith(".xls") || s.endsWith(".xlsx")){
                try {
                    filePathList.add(doProcessExcel(dto, poVo, s));
                } catch (IOException e) {
                    log.error("处理excel报告失败，{}",s, e);
                }
            }
        });
        return filePathList;
    }

    private String doProcessExcel(ExportWordDto dto, QueryProjectOperationExportVo poVo, String fileName)
        throws IOException {
        String filePath = StrUtil.join(File.separator, basePath,"template", fileName);
        String realFileName = String.format("%sDSMM认证评估表.xlsx", poVo.getOperationName());
        String path = String.join(File.separator, basePath, "temp", realFileName);
        FileUtil.touch(path);
        Map<String,Object> dataMap = new HashMap<>(16);
        List<SpecCalcResult> specCalcResults = specCalcResultService.list(new QueryWrapper<SpecCalcResult>().eq("operation_id", dto.getOperationId()));
        Map<String, BigDecimal> resMap = new HashMap<>(16);
        if (CollUtil.isNotEmpty(specCalcResults)){
            resMap = specCalcResults.stream().collect(Collectors.toMap(SpecCalcResult::getBpCode, SpecCalcResult::getScore));
        }

        // 查询能力分析模板关联的核查项
        final Map<AnalysisStandardVO, List<AnalysisStandardItemVO>> analysisStandardMap =
            analysisTemplateMapper.qryByTemplateType(1, null, 101).stream()
                .collect(Collectors.groupingBy(l -> BeanUtil.copyProperties(l, AnalysisStandardVO.class)));
        // 查询现状核验中的证明材料
        List<ItemFileMapDTO> itemFileMapList = questionnaireContentMapper.selectFileIdAndItemIdByOperationId(dto.getOperationId());
        Map<String, String> itemFileMap = new HashMap<>(16);
        if (CollUtil.isNotEmpty(itemFileMapList)){
            List<CoFile> coFiles = coFileMapper.queryFileByOperationId(dto.getOperationId());
            Map<String,String> fileMap = coFiles.stream().collect(Collectors.toMap(CoFile::getFileId,CoFile::getOriginalFileName));
            for (ItemFileMapDTO itemFileMapDTO : itemFileMapList){
                if (itemFileMap.containsKey(itemFileMapDTO.getItemId())){
                    String file = itemFileMap.get(itemFileMapDTO.getItemId());
                    if (StrUtil.isNotEmpty(file) && fileMap.get(itemFileMapDTO.getFileId()) != null) {
                        file = file + "," + fileMap.get(itemFileMapDTO.getFileId());
                    }
                    itemFileMap.put(itemFileMapDTO.getItemId(), file);
                } else {
                    itemFileMap.put(itemFileMapDTO.getItemId(), fileMap.get(itemFileMapDTO.getFileId()) == null ? "" : fileMap.get(itemFileMapDTO.getFileId()));
                }
            }
        }
        for (Map.Entry<AnalysisStandardVO,List<AnalysisStandardItemVO>> entry : analysisStandardMap.entrySet()){
            AnalysisStandardVO k = entry.getKey();
            List<AnalysisStandardItemVO> v = entry.getValue();
            String fileKey = k.getBpCode().replace(StrUtil.DOT,"").toLowerCase() + "file";
            String scoreKey = k.getBpCode().replace(StrUtil.DOT,"").toLowerCase() + "score";
            dataMap.put(scoreKey, resMap.get(k.getBpCode()));
            StringBuilder sb = new StringBuilder();
            v.stream().filter(analysisStandardItemVO -> StrUtil.isNotEmpty(analysisStandardItemVO.getItemId())).map(AnalysisStandardItemVO::getItemId).forEach(s -> {
                if (StrUtil.isEmpty(itemFileMap.get(s)) ){
                    return;
                }
                sb.append(itemFileMap.get(s)).append(StrUtil.DOT);
            });
            if (sb.length() > 0){
                dataMap.put(fileKey, sb.deleteCharAt(sb.lastIndexOf(StrUtil.DOT)).toString());
            } else {
                dataMap.put(fileKey, StrUtil.EMPTY);
            }
        }
        EasyExcel.write(path).withTemplate(filePath).sheet().doFill(dataMap);
        return path;
    }

    private String doProcessWord(ExportWordDto dto, QueryProjectOperationExportVo poVo, String fileName)
        throws IOException {
        String filePath = StrUtil.join(File.separator, basePath,"template", fileName);
        InputStream inputStream = Files.newInputStream(Paths.get(filePath));

        //数据模型
        Map<String, Object> model = new HashMap<>(16);

        //准备数据
        String operationId = dto.getOperationId();
        List<ExportWordChart> chartList = dto.getChartList();

        // 报告公共部分
        putCommonInfo(poVo, model);
        // 处理截图
        processChartList(model, chartList, operationId);

        //3.1.1能力评估结果
        //3.2详细评估结果
        capabilityAssessment(operationId, model);

        // 4 处置建议
        processSuggestions(operationId, model);

        //配置模板
        LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
        TOCRenderPolicy tocPolicy = new TOCRenderPolicy();
        Configure config =
            Configure.builder()
                .bind("dataSaftyHighSuggests", policy).bind("dataTecHighSuggests", policy)
                .bind("dataSaftyMidSuggests", policy).bind("dataTecMidSuggests", policy)
                .bind("specList", policy)
                .bind("tocContents", tocPolicy).build();

        //加载模板渲染数据
        XWPFTemplate template = XWPFTemplate.compile(inputStream, config).render(model);
        NiceXWPFDocument xwpfDocument = template.getXWPFDocument();
        xwpfDocument.enforceUpdateFields();

        String realFileName = String.format("%s专项报告.docx", poVo.getOperationName());
        String path = String.join(File.separator, basePath, "temp", realFileName);
        FileUtil.touch(path);

        //输出结果
        OutputStream out = Files.newOutputStream(Paths.get(path));
        BufferedOutputStream bos = new BufferedOutputStream(out);
        xwpfDocument.write(bos);
        bos.flush();
        out.flush();
        PoitlIOUtils.closeQuietlyMulti(template, xwpfDocument, bos, out);
        return path;
    }

    private void capabilityAssessment(String operationId, Map<String, Object> model) {
        String level = coVerificationMapper.queryMaxLevel(operationId);
        model.put("targetLevel", level);

        // 获取报告总览
        SpecOverviewVO specOverviewVO = coViewCustomerService.getSpecOverview(operationId);
        model.put("calcLevel", specOverviewVO.getResult());
        model.put("score", specOverviewVO.getTotalScore());

        SpecOverviewVO.CheckResult checkResult = specOverviewVO.getCheckResult();
        model.put("total", checkResult.getTotal());
        model.put("countA", checkResult.getCountA());
        model.put("countB", checkResult.getCountB());
        model.put("countC", checkResult.getCountC());
        model.put("countD", checkResult.getCountD());

        // bp得分
        List<QueryVerificationDetailDTO> bpDetails = coVerificationMapper.queryBpDetails(operationId);
        // 专项评估判断 需求：选择3级，列表分析内容展示3级与以下等级，其他等级同上；
        List<Integer> levelList = new ArrayList<>(LevelEnum.getUnderLevelCodeByLevel(level.trim()));

        // 切换schema
        pushVersion(operationId);
        // 查询能力分析模板关联的核查项
//        final Map<String, List<AnalysisStandardItemVO>> analysisStandardMap =
//            analysisTemplateMapper.qryByTemplateType(1, levelList, 101).stream()
//                .collect(Collectors.groupingBy(AnalysisStandardItemVO::getBpCode));
        // 手动添加的多个业务系统本质都是原系统调研的问题和内容，修复第二个业务系统中某个问题勾选了‘以上均不复核选项’，同时把第一个业务系统问卷中的内容也排除掉了的问题
        Set<String> excludeSet = questionnaireContentMapper.selectExcludeItems(operationId).stream().map(i -> i.getQuestionId() + i.getObjectId()).collect(Collectors.toSet());
//        Map<String, String> itemMap = questionnaireContentMapper.selectCheckedItems(operationId).stream()
//            .filter(q -> !excludeSet.contains(q.getQuestionId() + q.getObjectId())).collect(Collectors.toMap(QuestionnaireContent::getItemId,QuestionnaireContent::getItemTitle,(k1,k2)->k1));
        List<Map<String,Object>> specList = new ArrayList<>();
        int sort = 1;
        for (QueryVerificationDetailDTO detail : bpDetails){
            Map<String,Object> spec = new HashMap<>(16);
            spec.put("sort", sort);
            spec.put("process", detail.getProcess());
            spec.put("level", detail.getLevel());
            spec.put("dimension", detail.getGpDimension());
            spec.put("bpCode", detail.getBpCode());
            spec.put("content", detail.getStandardProvision());
//            List<AnalysisStandardItemVO> list = analysisStandardMap.get(detail.getBpCode());
//            StringBuilder sb = new StringBuilder();
//            if (CollUtil.isNotEmpty(list)){
//                list.forEach(analysisStandardItemVO -> {
//                    if (analysisStandardItemVO.getItemId() == null || itemMap.get(analysisStandardItemVO.getItemId()) == null){
//                        return;
//                    }
//                    sb.append(itemMap.get(analysisStandardItemVO.getItemId())).append(StrUtil.COMMA);
//                });
//            }
//            String suggest = "";
//            if (sb.length() > 0) {
//                suggest = sb.deleteCharAt(sb.lastIndexOf(StrUtil.COMMA)).toString();
//            }
            spec.put("suggest", detail.getDesc());
            spec.put("result", detail.getResult());
            spec.put("bpScore", detail.getBpScore());
            specList.add(spec);
            sort++;
        }
        model.put("specList", specList);
    }

    private void processChartList(Map<String, Object> model, List<ExportWordChart> chartList, String operationId) {
        List<Map<String, Object>> analysisContents = new ArrayList<>();
        String name = coGapAnalysisMapper.queryFileName(operationId);
        for (ExportWordChart chart : chartList) {
            if (chart.getName().equals("specOverviewImg")){
                Map<String, String> imageMap = getPicture(Lists.newArrayList(chart));
                model.put("specOverviewImg", getPictureStream(imageMap));
            }
            if (chart.getName().equals("lifecycleResultImg")){
                Map<String, String> imageMap = getPicture(Lists.newArrayList(chart));
                model.put("lifecycleResultImg", getPictureStream(imageMap));
            }
            if (chart.getName().equals("specRiskImg")){
                Map<String, String> imageMap = getPicture(Lists.newArrayList(chart));
                model.put("specRiskImg", getPictureStream(imageMap));
            }
            if (!chart.getName().startsWith(name)) {
                continue;
            }
            Map<String, Object> content = new HashMap<>();
            content.put("chartName", chart.getName());
            List<Map<String, Object>> chartImages = new ArrayList<>();
            Map<String, Object> chartMap = new HashMap<>();

            Map<String, String> imageMap = getPicture(Lists.newArrayList(chart));

            chartMap.put("chartImage", getPictureStream(imageMap));
            chartMap.put("chartImageName", chart.getName());
            chartImages.add(chartMap);
            content.put("chartImages", chartImages);
            analysisContents.add(content);
        }
        model.put("analysisContents", analysisContents);
    }

    @Override
    public ReportTypeEnum getType() {
        return ReportTypeEnum.SPEC;
    }
}
