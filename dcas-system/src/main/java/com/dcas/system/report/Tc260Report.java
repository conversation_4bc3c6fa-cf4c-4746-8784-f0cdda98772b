package com.dcas.system.report;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.enums.DataTagEnum;
import com.dcas.common.enums.FileType;
import com.dcas.common.enums.LabelEnum;
import com.dcas.common.utils.spring.SpringUtils;
import com.dcas.common.model.dto.ExportWordChart;
import com.dcas.common.model.dto.ExportWordDto;
import com.dcas.common.model.dto.StandardItemDTO;
import com.dcas.common.domain.entity.*;
import com.dcas.common.model.vo.AdviseSchemeVO;
import com.dcas.common.model.vo.FormConfigTreeVO;
import com.dcas.common.model.vo.QueryProjectOperationExportVo;
import com.dcas.common.model.vo.RiskAnalysisReportVO;
import com.dcas.common.mapper.*;
import com.dcas.system.report.attachment.AttachmentReportFactory;
import com.dcas.system.service.IRiskAnalysisService;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.deepoove.poi.plugin.toc.TOCRenderPolicy;
import com.deepoove.poi.util.PoitlIOUtils;
import com.deepoove.poi.xwpf.NiceXWPFDocument;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.*;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 江苏报告
 *
 * <AUTHOR>
 * @date 2024/01/08 16:00
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class Tc260Report extends AbstractReport implements ReportInterface {

    private final ModelFileMapper modelFileMapper;
    private final CoInventoryMapper coInventoryMapper;
    private final CoVerificationMapper coVerificationMapper;
    private final CoOperationMapper coOperationMapper;
    private final CoConstantMapper coConstantMapper;
    private final CoThreatAnalysisMapper coThreatAnalysisMapper;
    private final ThreatTreeMapper threatTreeMapper;
    private final QuestionnaireContentMapper questionnaireContentMapper;
    private final IRiskAnalysisService iRiskAnalysisService;
    private final CoFileMapper coFileMapper;
    private final DynamicProcessTreeMapper dynamicProcessTreeMapper;
    private final CoProgressMapper coProgressMapper;
    private final StandardItemMapper standardItemMapper;
    private final CoModelAnalysisResultMapper coModelAnalysisResultMapper;

    @Value("${safety.profile}")
    private String basePath;

    @Override
    public String exportWord(ExportWordDto dto, QueryProjectOperationExportVo poVo, Long modelId) throws Exception {
        List<String> filePathList = new ArrayList<>();
        CompletableFuture<String> future = CompletableFuture.allOf(CompletableFuture.supplyAsync(() -> {
            try {
                return process(dto, poVo, modelId);
            } catch (IOException e) {
                log.error("导出报告失败", e);
                return null;
            }
        }).whenComplete((v, th) -> {
            if (th != null) {
                log.error("", th);
            }
            if (v != null) {
                log.info(v);
                filePathList.add(v);
            }
        }), CompletableFuture.supplyAsync(() -> {
            try {
                dto.setNeedExcludeContent(getNeedExcludeContent());
                return ReportFactory.getReportHandler(ReportTypeEnum.DEFAULT).process(dto, poVo, null);
            } catch (Exception e) {
                log.error("导出报告失败", e);
                return null;
            }
        }).whenComplete((v, th) -> {
            if (th != null) {
                log.error("", th);
            }
            if (v != null) {
                log.info(v);
                filePathList.add(v);
            }
        }), CompletableFuture.supplyAsync(() -> {
            try {
                return AttachmentReportFactory.getAssetReportHandler(ReportTypeEnum.TC260).exportWord(dto, poVo);
            } catch (Exception e) {
                log.error("导出资产附件失败", e);
                return null;
            }
        }).whenComplete((v, th) -> {
            if (th != null) {
                log.error("", th);
            }
            if (v != null) {
                log.info(v);
                filePathList.add(v);
            }
        }), CompletableFuture.supplyAsync(() -> {
                try {
                    return  ReportFactory.getReportHandler(ReportTypeEnum.TEC_DETECTION).exportWord(dto, poVo);
                } catch (Exception e) {
                    log.error("导出技术检测报告失败", e);
                    return null;
                }
            }).whenComplete((v, th) -> {
                if (th != null) {
                    log.error("", th);
                }
                if (v != null) {
                    log.info(v);
                    filePathList.add(v);
                }
            }))
        .thenRun(() -> addScanReportToPath(filePathList, dto.getOperationId()))
        .thenApply(v -> zip(filePathList, poVo.getOperationName(), basePath));
        return future.get();
    }

    @Override
    @SchemaSwitch(ExportWordDto.class)
    public String process(ExportWordDto dto, QueryProjectOperationExportVo poVo, Long modelId) throws IOException {

        ModelFile modelFile = modelFileMapper.selectOne(new QueryWrapper<ModelFile>().eq("model_id", modelId));
        if (modelFile == null){
            String msg = String.format("找不到模型ID=%d对应的文件",modelId);
            throw new IOException(msg);
        }
        InputStream inputStream = new FileInputStream(modelFile.getFilePath());

        //数据模型
        Map<String, Object> model = new HashMap<>(16);

        //准备数据
        String operationId = dto.getOperationId();
        List<ExportWordChart> chartList = dto.getChartList();

        // 报告公共部分
        putCommonInfo(poVo, model);

        // 获取评估清单
        List<Map<String, Object>> processList = new ArrayList<>();
        List<Long> labelIds = coProgressMapper.selectAllLabelIds(operationId);
        Map<Long, List<DynamicProcessTree>> treeMap =
            dynamicProcessTreeMapper.selectList(new QueryWrapper<DynamicProcessTree>().eq("operation_id", operationId).in("tree_id", labelIds))
                .stream().collect(Collectors.groupingBy(DynamicProcessTree::getParentId));
        int i = 1;
        for (Map.Entry<Long, List<DynamicProcessTree>> entry: treeMap.entrySet()){
            Map<String, Object> map = new HashMap<>(16);
            map.put("sort", i++);
            map.put("name", LabelEnum.getNameByCode(entry.getKey()));
            map.put("content", entry.getValue().stream().map(DynamicProcessTree::getTreeName).collect(Collectors.joining("、")));
            map.put("finishDate", model.get("completedDate"));
            processList.add(map);
        }
        model.put("processList",processList);

        // 解析上传的信息调研清单
        parseInformationResearchFile(operationId,model);

        //《中华人民共和国网络安全法》
        //《中华人民共和国数据安全法》
        //《中华人民共和国个人信息保护法》
        //《网络安全标准实践指南 网络数据安全风险评估实施指引》
        //《信息安全技术 数据安全风险评估方法》（征求意见稿）
        String[] files = {"中华人民共和国网络安全法","中华人民共和国数据安全法","中华人民共和国个人信息保护法","网络安全标准实践指南 网络数据安全风险评估实施指引","《信息安全技术 数据安全风险评估方法》（征求意见稿）"};
        List<Map<String, Object>> fileRefList = Lists.newArrayList();
        List<String> templateFileList = coVerificationMapper.selectTemplateFileByOperationId(operationId);
        if (!CollectionUtils.isEmpty(templateFileList)) {
            templateFileList.forEach(s -> {
                Map<String, Object> fileMap = new HashMap<>(1);
                if (Arrays.asList(files).contains(s)){
                    return;
                }
                fileMap.put("name", s);
                fileRefList.add(fileMap);
            });
        }
        model.put("fileRefList", fileRefList);

        //查询资产盘点表
        QueryWrapper<CoInventory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", dto.getOperationId());
        queryWrapper.orderByDesc("bus_system");
        List<CoInventory> coInventoryList = coInventoryMapper.selectList(queryWrapper);

        // 数据安全风险汇总表
        List<Map<String, Object>> riskLevelList = new ArrayList<>();
        Map<String, Object> riskModel = new HashMap<>(16);
        riskModel.put("sort", "");
        riskModel.put("desc", "");
        riskModel.put("level", "");
        riskLevelList.add(riskModel);
        model.put("riskLevelList", riskLevelList);

        // 数据识别
        //3.资产梳理
        setModelAssetInfo(model, coInventoryList, operationId);

        // 3.5 安全措施情况
        safetyManagerInfo(model, operationId);

        // 4.1 风险识别
        riskIdentification(model, operationId);

        // 5.1 风险分析
        riskAnalysis(model, operationId, chartList);

        //威胁赋值清单
        List<CoThreatAnalysis> coThreatAnalyses =
            coThreatAnalysisMapper.selectList(new QueryWrapper<CoThreatAnalysis>().eq("operation_id", operationId));
        List<Map<String, Object>> threatList = new ArrayList<>();
        if (CollUtil.isNotEmpty(coThreatAnalyses)) {
            AtomicInteger sort = new AtomicInteger(1);
            coThreatAnalyses.stream()
                .collect(Collectors.toMap(CoThreatAnalysis::getBusSystem, Function.identity(), (k1, k2) -> k1))
                .forEach((k, v) -> {
                    // 正常一个业务系统配置一个威胁频率赋值
                    Map<String, Object> threatValueModel = new HashMap<>(16);
                    threatValueModel.put("sort", sort.get());
                    threatValueModel.put("busSystem", k);
                    threatValueModel.put("threatType", threatTreeMapper.selectThreatTreeListByType(v.getThreatType()));
                    threatValueModel.put("threatFrequency", getThreatLevelMap().get(v.getThreatFrequency()));
                    threatValueModel.put("threatValue", v.getThreatFrequency());
                    threatList.add(threatValueModel);
                    sort.getAndIncrement();
                });
        }
        model.put("threatList", threatList);

        // 5.2 整改建议
        processSuggestions(operationId, model);

        //配置模板
        LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
        TOCRenderPolicy tocPolicy = new TOCRenderPolicy();
        Configure config =
            Configure.builder().bind("riskLevelList", policy).bind("threatList", policy).bind("riskCalcResult", policy)
                .bind("dataSaftyHighSuggests", policy).bind("dataTecHighSuggests", policy)
                .bind("dataSaftyMidSuggests", policy).bind("dataTecMidSuggests", policy)
                .bind("dataSafeManagerList", policy).bind("personSafeManagerList", policy)
                .bind("dataSafeMergencyList", policy).bind("dataCollectList", policy).bind("dataTranslateList", policy)
                .bind("dataStorageList", policy).bind("dataProvisionList", policy).bind("dataUseList", policy)
                .bind("dataDisclosureList", policy).bind("dataDeleteList", policy).bind("dataExportList", policy)
                .bind("dataSafeManagerDescList", policy).bind("dataSafeTecDescList", policy)
                .bind("busSystemProductInfoList", policy)
                .bind("busSystemAppInfoList", policy)
                .bind("busSystemDataInfoList", policy)
                .bind("busSystemBaseInfoList", policy)
                .bind("dataCollectInfoList", policy)
                .bind("dataStorageInfoList", policy)
                .bind("dataTransportInfoList", policy)
                .bind("dataUseInfoList", policy)
                .bind("dataProviderInfoList", policy)
                .bind("dataDisclosureInfoList", policy)
                .bind("dataDeleteInfoList", policy)
                .bind("dataExportInfoList", policy)
                .bind("processList", policy)
                .bind("dataSafetyList", policy)
                .bind("normsList", policy)
                .bind("technologyList", policy)
                .bind("netList", policy)
                .bind("dataSaftySuggests", policy)
                .bind("dataProcessSuggests", policy)
                .bind("dataTecSuggests", policy)
                .bind("personInfoSuggests", policy)
                .bind("riskResultList", policy)
                .bind("tocContents", tocPolicy).build();

        //加载模板渲染数据
        XWPFTemplate template = XWPFTemplate.compile(inputStream, config).render(model);
        NiceXWPFDocument xwpfDocument = template.getXWPFDocument();
        xwpfDocument.enforceUpdateFields();

        String realFileName = String.format("%s报告-tc260专用.docx", poVo.getOperationName());
        String path = String.join(File.separator, basePath, "temp", realFileName);
        FileUtil.touch(path);

        //输出结果
        OutputStream out = new FileOutputStream(path);
        BufferedOutputStream bos = new BufferedOutputStream(out);
        xwpfDocument.write(bos);
        bos.flush();
        out.flush();
        PoitlIOUtils.closeQuietlyMulti(template, xwpfDocument, bos, out);
        return path;
    }

    private void riskAnalysis(Map<String, Object> model, String operationId, List<ExportWordChart> chartList) {
        RiskAnalysisReportVO vo = iRiskAnalysisService.queryRiskAnalysisReport(operationId, true);
        RiskAnalysisReportVO.AssetStatisticsChart assetStatisticsChart = vo.getAssetStatisticsChart();
        assetStatisticsChart.getDataList().forEach(reportConfigBaseDTO -> {
            if (reportConfigBaseDTO.getConfigName().startsWith("轻微")){
                model.put("lowerAssetNum", reportConfigBaseDTO.getConfigIndicatorValue());
            }
            if (reportConfigBaseDTO.getConfigName().startsWith("低")){
                model.put("lowAssetNum", reportConfigBaseDTO.getConfigIndicatorValue());
            }
            if (reportConfigBaseDTO.getConfigName().startsWith("中")){
                model.put("midAssetNum", reportConfigBaseDTO.getConfigIndicatorValue());
            }
            if (reportConfigBaseDTO.getConfigName().startsWith("高")){
                model.put("highAssetNum", reportConfigBaseDTO.getConfigIndicatorValue());
            }
            if (reportConfigBaseDTO.getConfigName().startsWith("重大")){
                model.put("largerAssetNum", reportConfigBaseDTO.getConfigIndicatorValue());
            }
        });
        // 综合风险值
        RiskAnalysisReportVO.ComprehensiveChart comprehensiveChart = vo.getComprehensiveChart();
        model.put("totalRiskValue", comprehensiveChart.getValue());
        double totalRiskValue = Double.parseDouble(comprehensiveChart.getValue());
        comprehensiveChart.getDataScopeList().forEach(scope -> {
            if (scope.getStartScope() <= totalRiskValue && scope.getEndScope() >= totalRiskValue){
                model.put("totalRiskLevel", scope.getDisplayName());
            }
        });

        // 业务系统风险排序
        RiskAnalysisReportVO.BusSystemRiskSortChart busSystemRiskSortChart = vo.getBusSystemRiskSortChart();
        List<Map<String, Object>> busSystemSortList = new ArrayList<>();
        busSystemRiskSortChart.getXAxisList().forEach(reportConfigBaseDTO -> {
            Map<String, Object> map = new HashMap<>(16);
            map.put("bsName", reportConfigBaseDTO.getConfigName());
            map.put("riskValue", reportConfigBaseDTO.getConfigIndicatorValue());
            busSystemSortList.add(map);
        });
        model.put("busSystemSortList", busSystemSortList);

        // 数据安全风险总览
        List<ExportWordChart> dataSecurityOverviewChartList =
            chartList.stream().filter(s -> "数据安全风险总览".equals(s.getName())).collect(Collectors.toList());
        Map<String, String> imageBase64Data1 = getPicture(dataSecurityOverviewChartList);
        putImage("dataSecurityOverviewImg", imageBase64Data1, model);

        //风险评估列表
        List<CoModelAnalysisResult> coModelAnalysisResultList = coModelAnalysisResultMapper
            .selectList(new QueryWrapper<CoModelAnalysisResult>().eq("operation_id", operationId));

        String titlesStr = "";
        List<Map> resultList = new ArrayList<>();
        for (CoModelAnalysisResult coModelAnalysisResult : coModelAnalysisResultList){
            titlesStr = coModelAnalysisResult.getTitles();
            List<Map> mapList = JSONUtil.toList(coModelAnalysisResult.getResult(), Map.class);
            resultList.addAll(mapList);
        }
        List<FormConfigTreeVO> titles = JSONUtil.toList(titlesStr, FormConfigTreeVO.class);
        Map<String, String> indexColumnMap = new HashMap<>(16);
        titles.forEach(formConfigTreeVO -> {
            if (formConfigTreeVO.getDataColumn()){
                String title = formConfigTreeVO.getTitle().trim();
                switch (title) {
                    case "所属业务系统":
                        indexColumnMap.put(formConfigTreeVO.getDataIndex(), "busSystemName");
                        break;
                    case "数据类型":
                        indexColumnMap.put(formConfigTreeVO.getDataIndex(), "dataType");
                        break;
                    case "数据级别":
                        indexColumnMap.put(formConfigTreeVO.getDataIndex(), "dataLevel");
                        break;
                    case "风险类型":
                        indexColumnMap.put(formConfigTreeVO.getDataIndex(), "riskType");
                        break;
                    case "风险危害程度":
                        indexColumnMap.put(formConfigTreeVO.getDataIndex(), "riskHazard");
                        break;
                    case "风险发生的可能性":
                        indexColumnMap.put(formConfigTreeVO.getDataIndex(), "riskOccurrence");
                        break;
                    case "风险等级":
                        indexColumnMap.put(formConfigTreeVO.getDataIndex(), "riskLevel");
                        break;
                    default:
                        break;
                }
            } else {
                if (CollUtil.isNotEmpty(formConfigTreeVO.getChildren())) {
                    formConfigTreeVO.getChildren().forEach(child -> {
                        if ("资产名称".equals(child.getTitle())) {
                            indexColumnMap.put(child.getDataIndex(), "assetName");
                        } else if ("敏感等级".equals(child.getTitle())) {
                            indexColumnMap.put(child.getDataIndex(), "sensitiveLevel");
                        }
                    });
                } else {
                    if ("所属业务系统".equals(formConfigTreeVO.getTitle())) {
                        indexColumnMap.put(formConfigTreeVO.getDataIndex(), "busSystemName");
                    }
                }
            }
        });
        List<Map<String, Object>> riskResultList = new ArrayList<>();
        // 如果超过100行记录，则截取前100
        if (resultList.size() > 100) {
            resultList = resultList.subList(0, 100);
        }
        resultList.forEach(map -> {
            Map<String, Object> resultMap = new HashMap<>(16);
            for (Object o : map.keySet()) {
                String key = (String)o;
                if (indexColumnMap.get(key) == null){
                    continue;
                }
                resultMap.put(indexColumnMap.get(key), map.get(key));
            }
            riskResultList.add(resultMap);
        });
        model.put("riskResultList", riskResultList);
    }

    @Override
    protected void processSuggestions(String operationId, Map<String, Object> model) {
        pushVersion(operationId);
        List<Map<String, Object>> dataSaftySuggests = new ArrayList<>();
        List<Map<String, Object>> dataProcessSuggests = new ArrayList<>();
        List<Map<String, Object>> dataTecSuggests = new ArrayList<>();
        List<Map<String, Object>> personInfoSuggests = new ArrayList<>();
        AdviseSchemeMapper adviseSchemeMapper = SpringUtils.getBean(AdviseSchemeMapper.class);
        String[] typeList = {"数据安全管理", "数据处理活动", "数据安全技术", "个人信息保护"};

        Arrays.asList(typeList).forEach(type -> {
            List<String> itemIds = standardItemMapper.queryItemIds(operationId, 1020, type);
            if (CollUtil.isEmpty(itemIds)){
                return;
            }
            // 按照处置建议type（管理方案、技术方案）分组
            Map<String, List<AdviseSchemeVO>> abilityItems =
                adviseSchemeMapper.selectSuggestsByOperationId(operationId, itemIds).stream()
                    .collect(Collectors.groupingBy(AdviseSchemeVO::getDescribe));

            if (CollUtil.isNotEmpty(abilityItems)) {
                AtomicInteger sort = new AtomicInteger(1);
                abilityItems.forEach((k1, v1) -> {
                    Map<String, Object> dataTec = new HashMap<>(16);
                    dataTec.put("sort", sort.get());
                    dataTec.put("item", k1);
                    dataTec.put("desc", getDesc(v1));
                    dataTec.put("priority",
                        v1.stream().max(Comparator.comparingInt(AdviseSchemeVO::getPriority)).map(adviseSchemeVO -> {
                            if (adviseSchemeVO.getPriority() == 3){
                                return "高";
                            } else if (adviseSchemeVO.getPriority() == 2){
                                return "中";
                            } else {
                                return "低";
                            }
                           }).orElse(""));
                    if ("数据安全管理".equals(type)) {
                        dataSaftySuggests.add(dataTec);
                    } else if ("数据处理活动".equals(type)){
                        dataProcessSuggests.add(dataTec);
                    }  else if ("数据安全技术".equals(type)){
                        dataTecSuggests.add(dataTec);
                    } else if ("个人信息保护".equals(type)){
                        personInfoSuggests.add(dataTec);
                    }
                    sort.getAndIncrement();
                });

            }
        });

        model.put("dataSaftySuggests", dataSaftySuggests);
        model.put("dataProcessSuggests", dataProcessSuggests);
        model.put("dataTecSuggests", dataTecSuggests);
        model.put("personInfoSuggests", personInfoSuggests);
    }

    private void riskIdentification(Map<String, Object> model, String operationId) {
        List<StandardItemDTO> list = standardItemMapper.queryItemsByStandardId(1020, operationId);
        List<Risk> risks = new ArrayList<>();
        list.stream().sorted(Comparator.comparingDouble(
            s -> Double.parseDouble(StrUtil.replace(String.valueOf(s.getSort()), StrUtil.DOT, StrUtil.EMPTY))))
            .collect(Collectors.groupingBy(StandardItemDTO::key)).forEach((key, all) -> {
            List<String> keyList = StrUtil.split(key, "_");
            List<Risk> dimensionRisks = new ArrayList<>();
            // 通过dimension分组，分类标记3
            all.stream().sorted(Comparator.comparingDouble(s -> Double.parseDouble(StrUtil.replace(
                StrUtil.isEmpty(s.getSortDimension()) || "-".equals(s.getSortDimension()) ? "0" :
                    String.valueOf(s.getSortDimension()), StrUtil.DOT, StrUtil.EMPTY))))
                .collect(Collectors.groupingBy(StandardItemDTO::dimensionKey))
                .forEach((dimensionKey, standardItems) -> {
                    List<String> dimensionKeyList = StrUtil.split(dimensionKey, "_");
                    List<Risk> classifyRisks = new ArrayList<>();
                    // 通过Classify分组，分类标记5
                    standardItems.stream().sorted(Comparator.comparingDouble(s -> Double.parseDouble(
                        StrUtil.isEmpty(s.getSortClassify()) || "-".equals(s.getSortClassify()) ? "0" :
                            StrUtil.replace(String.valueOf(s.getSortClassify()), StrUtil.DOT, StrUtil.EMPTY))))
                        .collect(Collectors.groupingBy(StandardItemDTO::classifyKey))
                        .forEach((classifyKey, contentList) -> {
                            List<String> classifyKeyList = StrUtil.split(classifyKey, "_");
                            Risk classifyRisk = Risk.builder().sort(classifyKeyList.get(0)).key(classifyKeyList.get(1))
                                .content(contentList.stream().map(StandardItemDTO::getContent).distinct().filter(Objects::nonNull)
                                    .collect(Collectors.joining("\n    "))).build();
                            classifyRisks.add(classifyRisk);
                        });
                    Risk dimensionRisk = Risk.builder().sort(dimensionKeyList.get(0)).key(dimensionKeyList.get(1))
                        .content(standardItems.stream().map(StandardItemDTO::getContent).distinct().filter(Objects::nonNull)
                            .collect(Collectors.joining("\n    "))).child(classifyRisks.stream().sorted(Comparator
                            .comparingDouble(s -> Double.parseDouble(
                                StrUtil.isEmpty(s.getSort()) || "-".equals(s.getSort()) ? "0" :
                                    StrUtil.replace(String.valueOf(s.getSort()), StrUtil.DOT, StrUtil.EMPTY))))
                            .collect(Collectors.toList())).build();
                    dimensionRisks.add(dimensionRisk);
                });
            Risk risk = Risk.builder().sort(keyList.get(0)).key(keyList.get(1)).child(dimensionRisks.stream().sorted(
                Comparator.comparingDouble(s -> Double.parseDouble(
                    StrUtil.isEmpty(s.getSort()) || "-".equals(s.getSort()) ? "0" :
                        StrUtil.replace(String.valueOf(s.getSort()), StrUtil.DOT, StrUtil.EMPTY))))
                .collect(Collectors.toList())).build();
            risks.add(risk);
        });
        List<Map<String, Object>> riskProcessList = new ArrayList<>();
        risks.stream().sorted(Comparator.comparingDouble(s -> Double.parseDouble(
            StrUtil.isEmpty(s.getSort()) || "-".equals(s.getSort()) ? "0" :
                StrUtil.replace(String.valueOf(s.getSort()), StrUtil.DOT, StrUtil.EMPTY)))).forEach(risk -> {
            Map<String, Object> riskProcessMap = new HashMap<>(16);
            riskProcessMap.put("process", risk.getKey());
            riskProcessMap.put("dimensionList", risk.getChild().stream().map(risk1 -> {
                Map<String, Object> dimensionMap = new HashMap<>(16);
                dimensionMap.put("dimension", risk1.getKey());
                List<Map<String,Object>> classifyList = risk1.getChild().stream()
                    .filter(risk2 -> StrUtil.isNotEmpty(risk2.getContent()) && StrUtil.isNotEmpty(risk2.getKey()))
                    .map(risk2 -> {
                        Map<String, Object> classifyMap = new HashMap<>(16);
                        classifyMap.put("classify", risk2.getKey());
                        classifyMap.put("content", risk2.getContent());
                        return classifyMap;
                    }).distinct().collect(Collectors.toList());
                dimensionMap.put("classifyList", classifyList);
                if (CollUtil.isEmpty(classifyList) || CollUtil.isEmpty(risk1.getChild())){
                    dimensionMap.put("content", risk1.getContent());
                }
                if (StrUtil.isEmpty(risk1.getContent())){
                    dimensionMap.put("content", "未识别相关风险，建议持续关注。");
                }
                return dimensionMap;
            }).collect(Collectors.toList()));
            riskProcessList.add(riskProcessMap);
        });
        model.put("riskProcessList", riskProcessList);
    }

    private void safetyManagerInfo(Map<String, Object> model, String operationId) {
        List<QuestionnaireContent> list = questionnaireContentMapper.selectCheckedItemsExcludeInapplicable(operationId);
        Map<String, List<QuestionnaireContent>> map = list.stream().collect(Collectors.groupingBy(QuestionnaireContent::getQuestionId));
        // 数据安全管理组织情况 对应题干1  340da314bcf7bf34d7108d154f905686
        List<Map<String, Object>> dataSafetyList = new ArrayList<>();
        AtomicInteger sort = new AtomicInteger(1);
        Set<String> duplicateSet = new HashSet<>();
        List<QuestionnaireContent> list1 = map.get("340da314bcf7bf34d7108d154f905686");
        if (CollUtil.isNotEmpty(list1)) {
            list1.forEach(questionnaireContent -> {
                if (duplicateSet.contains(questionnaireContent.getItemTitle())){
                    return;
                }
                Map<String, Object> dataSafetyMap = new HashMap<>(16);
                dataSafetyMap.put("sort", sort.getAndIncrement());
                dataSafetyMap.put("item", questionnaireContent.getItemTitle());
                duplicateSet.add(questionnaireContent.getItemTitle());
                dataSafetyList.add(dataSafetyMap);
            });
        }
        model.put("dataSafetyList", dataSafetyList);

        // 数据安全制度规范情况 对应题干2、题干3、题干4  （5ec82097d1d85ee09d3e7ebe6f16c408、8d1eea007e3837e906e5827ba4454316、b705981cf9c5ff14eb0be1c813919b4d）
        List<Map<String, Object>> normsList = new ArrayList<>();
        sort.set(1);
        List<QuestionnaireContent> list2 = map.get("5ec82097d1d85ee09d3e7ebe6f16c408");
        if (CollUtil.isNotEmpty(list2)) {
            list2.forEach(questionnaireContent -> {
                if (duplicateSet.contains(questionnaireContent.getItemTitle())) {
                    return;
                }
                Map<String, Object> normsMap = new HashMap<>(16);
                normsMap.put("sort", sort.getAndIncrement());
                normsMap.put("item", questionnaireContent.getItemTitle());
                duplicateSet.add(questionnaireContent.getItemTitle());
                normsList.add(normsMap);
            });
        }
        List<QuestionnaireContent> list3 = map.get("8d1eea007e3837e906e5827ba4454316");
        if (CollUtil.isNotEmpty(list3)) {
            list3.forEach(questionnaireContent -> {
                if (duplicateSet.contains(questionnaireContent.getItemTitle())){
                    return;
                }
                Map<String, Object> normsMap = new HashMap<>(16);
                normsMap.put("sort", sort.getAndIncrement());
                normsMap.put("item", questionnaireContent.getItemTitle());
                duplicateSet.add(questionnaireContent.getItemTitle());
                normsList.add(normsMap);
            });
        }
        List<QuestionnaireContent> list4 = map.get("b705981cf9c5ff14eb0be1c813919b4d");
        if (CollUtil.isNotEmpty(list4)) {
            list4.forEach(questionnaireContent -> {
                if (duplicateSet.contains(questionnaireContent.getItemTitle())){
                    return;
                }
                Map<String, Object> normsMap = new HashMap<>(16);
                normsMap.put("sort", sort.getAndIncrement());
                normsMap.put("item", questionnaireContent.getItemTitle());
                duplicateSet.add(questionnaireContent.getItemTitle());
                normsList.add(normsMap);
            });
        }
        model.put("normsList", normsList);

        // 数据安全技术措施情况 对应题干5  7bbf61d49499e0111eb2e7aa0adf1d41
        List<Map<String, Object>> technologyList = new ArrayList<>();
        sort.set(1);
        List<QuestionnaireContent> list5 = map.get("7bbf61d49499e0111eb2e7aa0adf1d41");
        if (CollUtil.isNotEmpty(list5)) {
            list5.forEach(questionnaireContent -> {
                if (duplicateSet.contains(questionnaireContent.getItemTitle())){
                    return;
                }
                Map<String, Object> technologyMap = new HashMap<>(16);
                technologyMap.put("sort", sort.getAndIncrement());
                technologyMap.put("item", questionnaireContent.getItemTitle());
                duplicateSet.add(questionnaireContent.getItemTitle());
                technologyList.add(technologyMap);
            });
        }
        model.put("technologyList", technologyList);

        // 网络安全管理和技术措施情况 对应题干6  8c8f15a34f096d0724cfdc3c9aadb361
        List<Map<String, Object>> netList = new ArrayList<>();
        sort.set(1);
        List<QuestionnaireContent> list6 = map.get("8c8f15a34f096d0724cfdc3c9aadb361");
        if (CollUtil.isNotEmpty(list6)) {
            list6.forEach(questionnaireContent -> {
                if (duplicateSet.contains(questionnaireContent.getItemTitle())){
                    return;
                }
                Map<String, Object> netMap = new HashMap<>(16);
                netMap.put("sort", sort.getAndIncrement());
                netMap.put("item", questionnaireContent.getItemTitle());
                duplicateSet.add(questionnaireContent.getItemTitle());
                netList.add(netMap);
            });
        }
        model.put("netList", netList);
    }

    private void parseInformationResearchFile(String operationId, Map<String, Object> model) {
        CoFile coFile = coFileMapper.queryFileByOperationIdAndFileType(operationId, FileType.RESEARCH.getCode());
        if (coFile == null){
            log.warn("调研文件清单未上传!");
            return;
        }
        List<Sheet> sheets = ExcelUtil.getReader(coFile.getFilePath()).getSheets();
        for (Sheet sheet : sheets){
            switch (sheet.getSheetName()){
                case "【评估单位信息】基本信息":
                    parseSheetOne(sheet, model);
                    break;
                case "【被评估方信息】数据处理者调研":
                    parseSheetTwo(sheet, model);
                    break;
                case "【被评估方信息】业务和信息系统调研":
                    parseSheetThree(sheet, model);
                    break;
                case "【被评估方信息】数据处理活动调研":
                    parseSheetFour(sheet, model);
                    break;
                default:
                    break;
            }
        }
    }

    private void parseSheetFour(Sheet sheet, Map<String, Object> model) {
        // 数据收集情况
        parseDataCollectInfo(sheet, model);

        // 数据存储情况
        parseDataStorageInfo(sheet, model);

        // 数据传输情况
        parseDataTransportInfo(sheet, model);

        // 数据使用和加工情况
        parseDataUsageInfo(sheet, model);

        // 数据提供情况
        parseDataProviderInfo(sheet, model);

        // 数据公开情况
        parseDataDisclosureInfo(sheet, model);

        // 数据删除情况
        parseDataDeleteInfo(sheet, model);

        // 数据出境情况
        parseDataExportInfo(sheet, model);
    }

    private void parseDataExportInfo(Sheet sheet, Map<String, Object> model) {
        Map<Integer, List<Map<String, Object>>> rowMap = new HashMap<>(16);
        getBusSystem(rowMap, "desc", sheet.getRow(51));
        List<Map<String, Object>> dataExportInfoList = new ArrayList<>();
        int sort = 1;
        for (Map.Entry<Integer, List<Map<String, Object>>> entry : rowMap.entrySet()){
            Map<String, Object> dataExportInfoMap = new HashMap<>(16);
            dataExportInfoMap.put("sort", sort++);
            entry.getValue().forEach(dataExportInfoMap::putAll);
            dataExportInfoList.add(dataExportInfoMap);
        }
        model.put("dataExportInfoList", dataExportInfoList);
    }

    private void parseDataDeleteInfo(Sheet sheet, Map<String, Object> model) {
        Map<Integer, List<Map<String, Object>>> rowMap = new HashMap<>(16);
        for (int k = 47; k <= 50; k++) {
            if (k == 47) {
                getBusSystem(rowMap, "purpose", sheet.getRow(k));
            } else if (k == 48){
                getBusSystem(rowMap, "way", sheet.getRow(k));
            } else if (k == 49){
                getBusSystem(rowMap, "achvied", sheet.getRow(k));
            } else{
                getBusSystem(rowMap, "isDel", sheet.getRow(k));
            }
        }
        List<Map<String, Object>> dataDeleteInfoList = new ArrayList<>();
        int sort = 1;
        for (Map.Entry<Integer, List<Map<String, Object>>> entry : rowMap.entrySet()){
            Map<String, Object> dataDeleteInfoMap = new HashMap<>(16);
            dataDeleteInfoMap.put("sort", sort++);
            entry.getValue().forEach(dataDeleteInfoMap::putAll);
            dataDeleteInfoList.add(dataDeleteInfoMap);
        }
        model.put("dataDeleteInfoList", dataDeleteInfoList);
    }

    private void parseDataDisclosureInfo(Sheet sheet, Map<String, Object> model) {
        Map<Integer, List<Map<String, Object>>> rowMap = new HashMap<>(16);
        for (int k = 40; k <= 46; k++) {
            if (k == 40) {
                getBusSystem(rowMap, "purpose", sheet.getRow(k));
            } else if (k == 41){
                getBusSystem(rowMap, "way", sheet.getRow(k));
            } else if (k == 42){
                getBusSystem(rowMap, "scope", sheet.getRow(k));
            }  else if (k == 43){
                getBusSystem(rowMap, "amount", sheet.getRow(k));
            } else if (k == 44){
                getBusSystem(rowMap, "industry", sheet.getRow(k));
            } else if (k == 45){
                getBusSystem(rowMap, "organization", sheet.getRow(k));
            } else{
                getBusSystem(rowMap, "region", sheet.getRow(k));
            }
        }
        List<Map<String, Object>> dataDisclosureInfoList = new ArrayList<>();
        int sort = 1;
        for (Map.Entry<Integer, List<Map<String, Object>>> entry : rowMap.entrySet()){
            Map<String, Object> dataDisclosureInfoMap = new HashMap<>(16);
            dataDisclosureInfoMap.put("sort", sort++);
            entry.getValue().forEach(dataDisclosureInfoMap::putAll);
            dataDisclosureInfoList.add(dataDisclosureInfoMap);
        }
        model.put("dataDisclosureInfoList", dataDisclosureInfoList);
    }

    private void parseDataProviderInfo(Sheet sheet, Map<String, Object> model) {
        Map<Integer, List<Map<String, Object>>> rowMap = new HashMap<>(16);
        for (int k = 29; k <= 39; k++) {
            if (k == 29) {
                getBusSystem(rowMap, "dataProviderPurpose", sheet.getRow(k));
            } else if (k == 30){
                getBusSystem(rowMap, "dataProviderWay", sheet.getRow(k));
            } else if (k == 31){
                getBusSystem(rowMap, "dataProviderScope", sheet.getRow(k));
            }  else if (k == 32){
                getBusSystem(rowMap, "dataRecipient", sheet.getRow(k));
            } else if (k == 33){
                getBusSystem(rowMap, "hasProtocol", sheet.getRow(k));
            } else if (k == 34){
                getBusSystem(rowMap, "external", sheet.getRow(k));
            } else if (k == 35){
                getBusSystem(rowMap, "externalType", sheet.getRow(k));
            } else if (k == 36){
                getBusSystem(rowMap, "amount", sheet.getRow(k));
            }  else if (k == 37){
                getBusSystem(rowMap, "scope", sheet.getRow(k));
            } else if (k == 38){
                getBusSystem(rowMap, "sensitiveLevel", sheet.getRow(k));
            } else {
                getBusSystem(rowMap, "saveTerm", sheet.getRow(k));
            }
        }
        List<Map<String, Object>> dataProviderInfoList = new ArrayList<>();
        int sort = 1;
        for (Map.Entry<Integer, List<Map<String, Object>>> entry : rowMap.entrySet()){
            Map<String, Object> dataProviderInfoMap = new HashMap<>(16);
            dataProviderInfoMap.put("sort", sort++);
            entry.getValue().forEach(dataProviderInfoMap::putAll);
            dataProviderInfoList.add(dataProviderInfoMap);
        }
        model.put("dataProviderInfoList", dataProviderInfoList);
    }

    private void parseDataUsageInfo(Sheet sheet, Map<String, Object> model) {
        Map<Integer, List<Map<String, Object>>> rowMap = new HashMap<>(16);
        for (int k = 20; k <= 28; k++) {
            if (k == 20) {
                getBusSystem(rowMap, "dataUsagePurpose", sheet.getRow(k));
            } else if (k == 21){
                getBusSystem(rowMap, "dataUsageWay", sheet.getRow(k));
            } else if (k == 22){
                getBusSystem(rowMap, "dataUsageScope", sheet.getRow(k));
            }  else if (k == 23){
                getBusSystem(rowMap, "dataUsageScene", sheet.getRow(k));
            } else if (k == 24){
                getBusSystem(rowMap, "dataUsageRule", sheet.getRow(k));
            } else if (k == 25){
                getBusSystem(rowMap, "dataUsageDept", sheet.getRow(k));
            } else if (k == 26){
                getBusSystem(rowMap, "dataUsageOther", sheet.getRow(k));
            } else if (k == 27){
                getBusSystem(rowMap, "dataUsageNetWork", sheet.getRow(k));
            } else {
                getBusSystem(rowMap, "dataUsageEntrust", sheet.getRow(k));
            }
        }
        List<Map<String, Object>> dataUseInfoList = new ArrayList<>();
        int sort = 1;
        for (Map.Entry<Integer, List<Map<String, Object>>> entry : rowMap.entrySet()){
            Map<String, Object> dataUseInfoMap = new HashMap<>(16);
            dataUseInfoMap.put("sort", sort++);
            entry.getValue().forEach(dataUseInfoMap::putAll);
            dataUseInfoList.add(dataUseInfoMap);
        }
        model.put("dataUseInfoList", dataUseInfoList);
    }

    private void parseDataTransportInfo(Sheet sheet, Map<String, Object> model) {
        Map<Integer, List<Map<String, Object>>> rowMap = new HashMap<>(16);
        for (int k = 16; k <= 19; k++) {
            if (k == 16) {
                getBusSystem(rowMap, "way", sheet.getRow(k));
            } else if (k == 17){
                getBusSystem(rowMap, "protocol", sheet.getRow(k));
            } else if (k == 18){
                getBusSystem(rowMap, "dataShare", sheet.getRow(k));
            } else {
                getBusSystem(rowMap, "dataApi", sheet.getRow(k));
            }
        }
        List<Map<String, Object>> dataTransportInfoList = new ArrayList<>();
        int sort = 1;
        for (Map.Entry<Integer, List<Map<String, Object>>> entry : rowMap.entrySet()){
            Map<String, Object> dataTransportInfoMap = new HashMap<>(16);
            dataTransportInfoMap.put("sort", sort++);
            entry.getValue().forEach(dataTransportInfoMap::putAll);
            dataTransportInfoList.add(dataTransportInfoMap);
        }
        model.put("dataTransportInfoList", dataTransportInfoList);
    }

    private void parseDataStorageInfo(Sheet sheet, Map<String, Object> model) {
        Map<Integer, List<Map<String, Object>>> rowMap = new HashMap<>(16);
        for (int k = 12; k <= 15; k++) {
            if (k == 12) {
                getBusSystem(rowMap, "name", sheet.getRow(k));
            } else if (k == 13){
                getBusSystem(rowMap, "address", sheet.getRow(k));
            } else if (k == 14){
                getBusSystem(rowMap, "term", sheet.getRow(k));
            } else {
                getBusSystem(rowMap, "strategy", sheet.getRow(k));
            }
        }
        List<Map<String, Object>> dataStorageInfoList = new ArrayList<>();
        int sort = 1;
        for (Map.Entry<Integer, List<Map<String, Object>>> entry : rowMap.entrySet()){
            Map<String, Object> dataStorageInfoMap = new HashMap<>(16);
            dataStorageInfoMap.put("sort", sort++);
            entry.getValue().forEach(dataStorageInfoMap::putAll);
            dataStorageInfoList.add(dataStorageInfoMap);
        }
        model.put("dataStorageInfoList", dataStorageInfoList);
    }

    private void parseDataCollectInfo(Sheet sheet, Map<String, Object> model) {
        Map<Integer, List<Map<String, Object>>> rowMap = new HashMap<>(16);
        for (int k = 3; k <= 11; k++) {
            if (k == 3) {
                getBusSystem(rowMap, "channel", sheet.getRow(k));
            } else if (k == 4){
                getBusSystem(rowMap, "way", sheet.getRow(k));
            } else if (k == 5){
                getBusSystem(rowMap, "scope", sheet.getRow(k));
            } else if (k == 6){
                getBusSystem(rowMap, "purpose", sheet.getRow(k));
            }  else if (k == 7){
                getBusSystem(rowMap, "frequency", sheet.getRow(k));
            }  else if (k == 8){
                getBusSystem(rowMap, "outerSource", sheet.getRow(k));
            }  else if (k == 9){
                getBusSystem(rowMap, "hasProtocol", sheet.getRow(k));
            }  else if (k == 10){
                getBusSystem(rowMap, "relSystem", sheet.getRow(k));
            } else {
                getBusSystem(rowMap, "outerDevice", sheet.getRow(k));
            }
        }
        List<Map<String, Object>> dataCollectInfoList = new ArrayList<>();
        int sort = 1;
        for (Map.Entry<Integer, List<Map<String, Object>>> entry : rowMap.entrySet()){
            Map<String, Object> dataCollectInfoMap = new HashMap<>(16);
            dataCollectInfoMap.put("sort", sort++);
            entry.getValue().forEach(dataCollectInfoMap::putAll);
            dataCollectInfoList.add(dataCollectInfoMap);
        }
        model.put("dataCollectInfoList", dataCollectInfoList);
    }

    private void parseSheetThree(Sheet sheet, Map<String, Object> model) {
        for (int i = 3; i <= 5; i++) {
            if (i == 3) {
                // 网络规模
                model.put("busSystemNetworkScale", getCellValue(sheet.getRow(i).getCell(4)));
            } else if (i == 4) {
                // 拓扑结构
                model.put("busSystemNetworkTopology", getCellValue(sheet.getRow(i).getCell(4)));
            } else {
                // 信息系统对外连接情况
                model.put("busSystemConnectInfo", getCellValue(sheet.getRow(i).getCell(4)));
            }
        }

        // 业务系统基本信息
        // 业务系统基本信息
        Map<Integer, Object> busSystemMap = new HashMap<>(16);
        parseBusSystemBaseInfo(sheet, model, busSystemMap);

        // 业务涉及数据情况 第14-18行
//        parseBusSystemDataInfo(sheet, model);

        //信息系统、App 和小程序情况 第20-25行
        parseBusSystemAppInfo(sheet, model, busSystemMap);

        // 接入的外部第三方产品、服务或 SDK 的情况
        parseBusSystemProductInfo(sheet, model, busSystemMap);
    }

    private void parseBusSystemProductInfo(Sheet sheet, Map<String, Object> model, Map<Integer, Object> busSystemMap) {
        Map<Integer, List<Map<String, Object>>> rowMap = new HashMap<>(16);
        for (int k = 27; k <= 31; k++) {
            if (k == 27) {
                getBusSystem(rowMap, "name", sheet.getRow(k));
            } else if (k == 28){
                getBusSystem(rowMap, "release", sheet.getRow(k));
            } else if (k == 29){
                getBusSystem(rowMap, "provider", sheet.getRow(k));
            } else if (k == 30){
                getBusSystem(rowMap, "purpose", sheet.getRow(k));
            } else {
                getBusSystem(rowMap, "sign", sheet.getRow(k));
            }
        }
        List<Map<String, Object>> busSystemProductInfoList = new ArrayList<>();
        int sort = 1;
        for (Map.Entry<Integer, List<Map<String, Object>>> entry : rowMap.entrySet()){
            Map<String, Object> productInfoMap = new HashMap<>(16);
            productInfoMap.put("sort", sort++);
            productInfoMap.put("busSystemName", busSystemMap.get(entry.getKey()));
            entry.getValue().forEach(productInfoMap::putAll);
            busSystemProductInfoList.add(productInfoMap);
        }
        model.put("busSystemProductInfoList", busSystemProductInfoList);
    }

    private void parseBusSystemAppInfo(Sheet sheet, Map<String, Object> model, Map<Integer, Object> busSystemMap) {
        Map<Integer, List<Map<String, Object>>> rowMap = new HashMap<>(16);
        for (int k = 20; k <= 25; k++) {
            if (k == 20) {
                getBusSystem(rowMap, "appFunction", sheet.getRow(k));
            } else if (k == 21){
                getBusSystem(rowMap, "appResult", sheet.getRow(k));
            } else if (k == 22){
                getBusSystem(rowMap, "appUrl", sheet.getRow(k));
            } else if (k == 23){
                getBusSystem(rowMap, "appConnect", sheet.getRow(k));
            } else if (k == 24){
                getBusSystem(rowMap, "appDataApi", sheet.getRow(k));
            } else {
                getBusSystem(rowMap, "appName", sheet.getRow(k));
            }
        }
        List<Map<String, Object>> busSystemAppInfoList = new ArrayList<>();
        int sort = 1;
        for (Map.Entry<Integer, List<Map<String, Object>>> entry : rowMap.entrySet()){
            Map<String, Object> appInfoMap = new HashMap<>(16);
            appInfoMap.put("sort", sort++);
            appInfoMap.put("busSystemName", busSystemMap.get(entry.getKey()));
            entry.getValue().forEach(appInfoMap::putAll);
            busSystemAppInfoList.add(appInfoMap);
        }
        model.put("busSystemAppInfoList", busSystemAppInfoList);
    }

    private void parseBusSystemDataInfo(Sheet sheet, Map<String, Object> model) {
        Map<Integer, List<Map<String, Object>>> rowMap = new HashMap<>(16);
        for (int k = 15; k <= 18; k++) {
            if (k == 15) {
                getBusSystem(rowMap, "busSystemName", sheet.getRow(k));
            } else if (k == 16){
                getBusSystem(rowMap, "busSystemDesc", sheet.getRow(k));
            } else if (k == 17){
                getBusSystem(rowMap, "busSystemType", sheet.getRow(k));
            }  else {
                getBusSystem(rowMap, "busSystemRelDept", sheet.getRow(k));
            }
        }
        List<Map<String, Object>> busSystemDataInfoList = new ArrayList<>();
        int sort = 1;
        for (Map.Entry<Integer, List<Map<String, Object>>> entry : rowMap.entrySet()){
            Map<String, Object> busSystemMap = new HashMap<>(16);
            busSystemMap.put("sort", sort++);
            entry.getValue().forEach(busSystemMap::putAll);
            busSystemDataInfoList.add(busSystemMap);
        }
        model.put("busSystemDataInfoList", busSystemDataInfoList);
    }

    private void parseBusSystemBaseInfo(Sheet sheet, Map<String, Object> model, Map<Integer, Object> busSystemMap) {
        Map<Integer, List<Map<String, Object>>> rowMap = new HashMap<>(16);
        for (int k = 7; k <= 14; k++) {
            if (k == 7) {
                getBusSystem(rowMap, "busSystemName", sheet.getRow(k));
            } else if (k == 8){
                getBusSystem(rowMap, "busSystemDesc", sheet.getRow(k));
            } else if (k == 9){
                getBusSystem(rowMap, "busSystemType", sheet.getRow(k));
            } else if (k == 10){
                getBusSystem(rowMap, "busSystemObject", sheet.getRow(k));
            } else if (k == 11){
                getBusSystem(rowMap, "busSystemProcess", sheet.getRow(k));
            } else if (k == 12){
                getBusSystem(rowMap, "busSystemUserSize", sheet.getRow(k));
            } else if (k == 13){
                getBusSystem(rowMap, "busSystemRegion", sheet.getRow(k));
            } else {
                getBusSystem(rowMap, "busSystemRelDept", sheet.getRow(k));
            }
        }
        List<Map<String, Object>> busSystemBaseInfoList = new ArrayList<>();
        int sort = 1;
        for (Map.Entry<Integer, List<Map<String, Object>>> entry : rowMap.entrySet()){
            Map<String, Object> baseMap = new HashMap<>(16);
            baseMap.put("sort", sort++);
            entry.getValue().forEach(map -> {
                baseMap.putAll(map);
                if (map.containsKey("busSystemName")){
                    busSystemMap.put(entry.getKey(), map.get("busSystemName"));
                }
            });
            busSystemBaseInfoList.add(baseMap);
        }
        model.put("busSystemBaseInfoList", busSystemBaseInfoList);
    }

    private void getBusSystem(Map<Integer, List<Map<String, Object>>> rowMap, String key, Row row) {
        for (int j = 4; j <= row.getLastCellNum(); j++) {
            if (row.getCell(j) == null) {
                continue;
            }
            String value = (String)getCellValue(row.getCell(j));
            if (StrUtil.isEmpty(value)){
                continue;
            }
            Map<String, Object> map = new HashMap<>(16);
            map.put(key, value);
            if (rowMap.containsKey(j)){
                rowMap.get(j).add(map);
            } else {
                List<Map<String, Object>> rowList = new ArrayList<>();
                rowList.add(map);
                rowMap.put(j, rowList);
            }
        }
    }

    private void parseSheetTwo(Sheet sheet, Map<String, Object> model) {
        // sheet1从第二行开始
        for (int i=3; i<=sheet.getLastRowNum(); i++) {
            if (i == 3) {
                // 数据处理者单位名称、组织机构代码、办公地址
                model.put("dataProcessorUnitName", getCellValue(sheet.getRow(i).getCell(3)));
                model.put("dataProcessorUnitCode", getCellValue(sheet.getRow(i).getCell(5)));
                model.put("dataProcessorUnitAddress", getCellValue(sheet.getRow(i).getCell(7)));
            } else if (i == 4) {
                // 数据处理者法人代表、人员规模、经营范围
                model.put("dataProcessorLegalPerson", getCellValue(sheet.getRow(i).getCell(3)));
                model.put("dataProcessorStaffSize", getCellValue(sheet.getRow(i).getCell(5)));
                model.put("dataProcessorBusinessScope", getCellValue(sheet.getRow(i).getCell(7)));
            } else if (i == 5) {
                // 数据处理者项目联系人、项目联系人联系方式、邮政编码
                model.put("expediter", getCellValue(sheet.getRow(i).getCell(3)));
                model.put("expediterContact", getCellValue(sheet.getRow(i).getCell(5)));
                model.put("expediterPostalCode", getCellValue(sheet.getRow(i).getCell(7)));
            }  else if (i == 6) {
                // 数据处理者项目联系人所属部门、项目联系人职务、数据安全负责人（*）
                model.put("expediterDept", getCellValue(sheet.getRow(i).getCell(3)));
                model.put("expediterDuty", getCellValue(sheet.getRow(i).getCell(5)));
                model.put("expediterSafty", getCellValue(sheet.getRow(i).getCell(7)));
            } else if (i == 7){
                // 单位类型（党政机关、事业单位、企业、社会团体、其他）
                model.put("dataProcessorUnitType", getCellValue(sheet.getRow(i).getCell(3)));
            } else if (i == 8){
                // 特定类型数据处理者（如政务数据处理者、大型网络平台运营者、关键信息基础设施运营者等）
                model.put("dataProcessorSpecial", getCellValue(sheet.getRow(i).getCell(3)));
            } else if (i == 9){
                // 所属行业领域
                //（公共通信和信息服务、能源、交通、水利、金融、公共服务、电子政务、国防科技工业、卫生健康、教育、科技、文化、农业等）
                model.put("dataProcessorIndustrySector", getCellValue(sheet.getRow(i).getCell(3)));
            } else if (i == 10){
                // 上级主管部门
                model.put("dataProcessorParentDept", getCellValue(sheet.getRow(i).getCell(3)));
            } else if (i == 11){
                // 业务运营地区
                model.put("dataProcessorZone", getCellValue(sheet.getRow(i).getCell(3)));
            } else if (i == 12){
                // 主要业务范围、业务规模
                model.put("dataProcessorBusinessScope", getCellValue(sheet.getRow(i).getCell(3)));
            } else if (i == 13){
                // 数据处理相关服务取得行政许可的情况
                model.put("dataProcessorLicense", getCellValue(sheet.getRow(i).getCell(3)));
            } else if (i == 14){
                // 资本组成和实际控制人情况
                model.put("dataProcessorControlPerson", getCellValue(sheet.getRow(i).getCell(3)));
            } else if (i == 15){
                // 境外上市或计划赴境外上市及境外资本参与情况
                //（是，上市地区和交易所；计划上市，计划上市地区和交易所；以协议控制（VIE）架构等方式实质性境外上市；否）
                model.put("dataProcessorIPO", getCellValue(sheet.getRow(i).getCell(3)));
            }
        }
    }

    private void parseSheetOne(Sheet sheet, Map<String, Object> model) {
        // sheet1从第二行开始
        for (int i=4; i<=sheet.getLastRowNum(); i++) {
            if (i == 4) {
                // 评估单位名称
                model.put("evaluationUnitName", getCellValue(sheet.getRow(i).getCell(3)));
            } else if (i == 5) {
                // 评估单位地址
                model.put("evaluationUnitAddress", getCellValue(sheet.getRow(i).getCell(3)));
            } else if (i == 6) {
                // 邮政编码
                model.put("evaluationUnitPostalCode", getCellValue(sheet.getRow(i).getCell(3)));
            }
        }
    }

    private Object getCellValue(Cell cell){
        if (cell.getCellType() == CellType.NUMERIC){
            Object val = cell.getNumericCellValue();
            if (DateUtil.isCellDateFormatted(cell)) {
                val = DateUtil.getJavaDate((Double) val); // POI Excel 日期格式转换
            } else {
                if ((Double) val % 1 != 0) {
                    val = new BigDecimal(val.toString());
                } else {
                    val = new DecimalFormat("0").format(val);
                }
            }
            return val;
        } else if (cell.getCellType() == CellType.BOOLEAN){
            return cell.getBooleanCellValue();
        } else if (cell.getCellType() == CellType.FORMULA){
            return cell.getCellFormula();
        } else {
            return cell.getStringCellValue();
        }
    }

    private Map<Integer, String> getThreatLevelMap() {
        Map<Integer, String> threatLevelMap = new HashMap<>(16);
        threatLevelMap.put(5, "很高");
        threatLevelMap.put(4, "高");
        threatLevelMap.put(3, "中等");
        threatLevelMap.put(2, "低");
        threatLevelMap.put(1, "很低");
        return threatLevelMap;
    }

    private void setModelAssetInfo(Map<String, Object> model, List<CoInventory> coInventories, String operationId) {
        //数据资产总条数
        int dataAssetsTotal = coInventories.size();
        //高敏感资产
        List<CoInventory> highSensitiveAssetsList = new ArrayList<>();
        //中敏感资产
        List<CoInventory> midSensitiveAssetsList = new ArrayList<>();
        //低敏感资产
        List<CoInventory> lowSensitiveAssetsList = new ArrayList<>();
        QueryWrapper<CoConstant> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("customer_id", coOperationMapper.selectCustomIdByOperationId(operationId));
        List<CoConstant> coConstants = coConstantMapper.selectList(queryWrapper);
        //客户最高敏感等级
        int level = coConstants.get(0).getHighestSensitiveLevel();
        //客户最高敏感等级
        sensitiveAssetProcess(level, coInventories, highSensitiveAssetsList, midSensitiveAssetsList,
            lowSensitiveAssetsList);

        model.put("dataAssetsNum", dataAssetsTotal);
        // 核心、重要、一般资产数量
        if (CollUtil.isNotEmpty(coInventories)) {
            int coreData = 0;
            int majorData = 0;
            int generalData = 0;
            for (CoInventory coInventory : coInventories) {
                if (Objects.equals(coInventory.getDataTag(), DataTagEnum.CORE.getTag())) {
                    coreData++;
                } else if (Objects.equals(coInventory.getDataTag(), DataTagEnum.IMPORTANT.getTag())) {
                    majorData++;
                } else if (Objects.equals(coInventory.getDataTag(), DataTagEnum.GENERAL.getTag())) {
                    generalData++;
                }
            }
            model.put("coreData", coreData);
            model.put("majorData", majorData);
            model.put("generalData", generalData);
        }

        // 最高敏感级别
        int maxLevel =
            coInventories.stream().map(CoInventory::getSensitiveLevel).max(Comparator.comparingInt(s -> s)).orElse(0);
        model.put("maxLevel", maxLevel);

        Map<Integer, Long> assetSensitiveGroup = coInventories.stream().collect(Collectors.groupingBy(CoInventory::getSensitiveLevel, Collectors.counting()));
        Map<String, Double> map = new TreeMap<>(Comparator.comparing(String::valueOf).reversed());
        for (int i = 1; i <= 5; i++) {
            Long levelNum = assetSensitiveGroup.getOrDefault(i, 0L);
            map.put(i+"", NumberUtil.div(levelNum * 100, dataAssetsTotal, 1));
        }
        model.put("level1", map.get("1"));
        model.put("level2", map.get("2"));
        model.put("level3", map.get("3"));
        model.put("level4", map.get("4"));
        model.put("level5", map.get("5"));
    }

    /**
     * 默认模板需排除的章节内容
     */
    @Override
    public Set<LabelEnum> getNeedExcludeContent() {
        return CollUtil.newHashSet(LabelEnum.SMZQ);
    }

    @Override
    public ReportTypeEnum getType() {
        return ReportTypeEnum.TC260;
    }

    @Builder
    @Getter
    @Setter
    public static class Risk {
        private String sort;

        private String content;

        private String key;

        private List<Risk> child;
    }
}
