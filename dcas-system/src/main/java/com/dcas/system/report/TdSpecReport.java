package com.dcas.system.report;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.domain.entity.*;
import com.dcas.common.enums.DataTagEnum;
import com.dcas.common.enums.LabelEnum;
import com.dcas.common.enums.OptEnum;
import com.dcas.common.mapper.*;
import com.dcas.common.model.dto.*;
import com.dcas.common.model.excel.TdSpecRiskExcel;
import com.dcas.common.model.vo.*;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.deepoove.poi.plugin.toc.TOCRenderPolicy;
import com.deepoove.poi.util.PoitlIOUtils;
import com.deepoove.poi.xwpf.NiceXWPFDocument;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @className TdSpecReport
 * @description 电信领域数据安全专项
 * @date 2024/07/01 18:21
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TdSpecReport extends AbstractReport implements ReportInterface{

    private final SpecialEvaluationConfigMapper specialEvaluationConfigMapper;
    private final CoVerificationMapper coVerificationMapper;
    private final LawItemMapper lawItemMapper;
    private final CoLegalMapper coLegalMapper;
    private final TagMapper tagMapper;
    private final CoModelAnalysisResultMapper coModelAnalysisResultMapper;
    private final MidThreatResultMapper midThreatResultMapper;
    private final CoInventoryMapper coInventoryMapper;
    private final AdviseRiskMapper adviseRiskMapper;
    private final DynamicProcessTreeMapper dynamicProcessTreeMapper;


    @Value("${safety.profile}")
    private String basePath;

    @Override
    public String exportWord(ExportWordDto dto, QueryProjectOperationExportVo poVo) throws Exception {
        List<String> filePathList = new ArrayList<>();
        CompletableFuture<String> future = CompletableFuture.allOf(CompletableFuture.supplyAsync(() -> {
            try {
                return process(dto, poVo);
            } catch (IOException e) {
                log.error("导出专项报告失败", e);
                return null;
            }
        }).whenComplete((v, th) -> {
            if (th != null) {
                log.error("", th);
            }
            if (v != null) {
                log.info(v.toString());
                filePathList.addAll(v);
            }
        })).thenApply(v -> zip(filePathList, poVo.getOperationName(), basePath));
        return future.get();
    }

    @SchemaSwitch(value = ExportWordDto.class)
    @Override
    public List<String> process(ExportWordDto dto, QueryProjectOperationExportVo poVo) throws IOException {
        SpecialEvaluationConfig specialEvaluationConfig = specialEvaluationConfigMapper.selectById(poVo.getSpecId());
        if (specialEvaluationConfig.getReportPath() == null){
            return new ArrayList<>();
        }
        List<String> fileNames = StrUtil.split(specialEvaluationConfig.getReportPath(),StrUtil.C_COMMA);
        List<String> filePathList = new ArrayList<>();
        fileNames.forEach(s -> {
            if (s.endsWith(".doc") || s.endsWith(".docx")){
                try {
                    filePathList.add(doProcessWord(dto, poVo, s));
                } catch (IOException e) {
                    log.error("处理word报告失败,{}",s, e);
                }
            } else if (s.endsWith(".xls") || s.endsWith(".xlsx")){
                try {
                    filePathList.add(doProcessExcel(dto, poVo, s));
                } catch (IOException e) {
                    log.error("处理excel报告失败，{}",s, e);
                }
            }
        });
        return filePathList;
    }

    private String doProcessExcel(ExportWordDto dto, QueryProjectOperationExportVo poVo, String fileName)  throws IOException {
        String filePath = StrUtil.join(File.separator, basePath,"template", fileName);
        String realFileName = String.format("%s-电信领域综合风险分析清单.xlsx", poVo.getOperationName());
        String path = String.join(File.separator, basePath, "temp", realFileName);
        List<TdSpecRiskExcel> list = getRiskResult(dto.getOperationId(), false);
        EasyExcelFactory.write(path).withTemplate(filePath).sheet().doWrite(list);
        return path;
    }

    private List<TdSpecRiskExcel> getRiskResult(String operationId, boolean limitTop100) {
        List<CoModelAnalysisResult> coModelAnalysisResultList = coModelAnalysisResultMapper
            .selectList(new QueryWrapper<CoModelAnalysisResult>().eq("operation_id", operationId));

        List<Map> resultList = new ArrayList<>();
        for (CoModelAnalysisResult coModelAnalysisResult : coModelAnalysisResultList){
            List<Map> mapList = JSONUtil.toList(coModelAnalysisResult.getResult(), Map.class);
            resultList.addAll(mapList);
        }
        /**
         * 		"0": "OB",                  -- 所属业务系统
         * 		"200": "TESTDDL",           -- 数据资产名称
         * 		"201": "",                  -- 数据资产描述
         * 		"300": "3",                 -- 网络环境和技术措施
         * 		"3": "3",                   -- 可能性级别
         * 		"202": "一般数据",            -- 数据属性标识
         * 		"301": "2",                 -- 管理制度和处理流程
         * 		"400": "1",                 -- 安全影响级别
         * 		"302": "1",                 -- 参与人员和获取方管理
         * 		"5": "高",                   -- 安全风险等级
         * 		"303": "1",                  -- 安全态势
         * 		"id": "749069997896105984"
         */
        List<TdSpecRiskExcel> list = new ArrayList<>();
        resultList.forEach(map -> {
            TdSpecRiskExcel excel = new TdSpecRiskExcel();
            excel.setBusSystem((String)map.get("0"));
            excel.setAssetName((String)map.get("200"));
            excel.setAssetTag((String)map.get("202"));
            excel.setTechLikeHood((String)map.get("300"));
            excel.setManageLikeHood((String)map.get("301"));
            excel.setRoleLikeHood((String)map.get("302"));
            excel.setLikeHood((String)map.get("303"));
            excel.setPossibleLevel((String)map.get("3"));
            excel.setImpactLevel(map.get("400") == null ? (String)map.get("500") :  (String)map.get("400") );
            excel.setRiskLevel((String)map.get("5"));
            list.add(excel);
        });
        List<String> riskLevelSort = Lists.newArrayList("极高", "高", "中", "低");
        AtomicInteger sort = new AtomicInteger(1);
        if (limitTop100) {
            return list.stream().sorted(Comparator.comparing(excel -> riskLevelSort.indexOf(excel.getRiskLevel())))
                .limit(100).peek(t -> t.setSort(sort.getAndIncrement())).collect(Collectors.toList());
        } else {
            return list.stream().sorted(Comparator.comparing(excel -> riskLevelSort.indexOf(excel.getRiskLevel())))
                .peek(t -> t.setSort(sort.getAndIncrement())).collect(Collectors.toList());
        }
    }

    private String doProcessWord(ExportWordDto dto, QueryProjectOperationExportVo poVo, String fileName)
        throws IOException {
        String filePath = StrUtil.join(File.separator, basePath,"template", fileName);
        InputStream inputStream = Files.newInputStream(Paths.get(filePath));

        //数据模型
        Map<String, Object> model = new HashMap<>(16);

        //准备数据
        String operationId = dto.getOperationId();
        List<ExportWordChart> chartList = dto.getChartList();

        // 报告公共部分
        putCommonInfo(poVo, model);

        //4.合规性评估
        lawAssessment(operationId, model);

        // 4 处置建议
        processSuggestions(operationId, model);

        // 5 安全风险分析
        processRisk(operationId, model);

        //配置模板
        LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
        TOCRenderPolicy tocPolicy = new TOCRenderPolicy();
        Configure config =
            Configure.builder()
                .bind("dataSaftyHighSuggests", policy).bind("dataTecHighSuggests", policy)
                .bind("dataSaftyMidSuggests", policy).bind("dataTecMidSuggests", policy)
                .bind("lawItems", policy)
                .bind("riskList", policy)
                .bind("qualitativeRiskList", policy)
                .bind("impactList", policy)
                .bind("riskResultList", policy)
                .bind("tocContents", tocPolicy).build();

        //加载模板渲染数据
        XWPFTemplate template = XWPFTemplate.compile(inputStream, config).render(model);
        NiceXWPFDocument xwpfDocument = template.getXWPFDocument();
        xwpfDocument.enforceUpdateFields();

        String realFileName = String.format("%s专项报告.docx", poVo.getOperationName());
        String path = String.join(File.separator, basePath, "temp", realFileName);
        FileUtil.touch(path);

        //输出结果
        OutputStream out = Files.newOutputStream(Paths.get(path));
        BufferedOutputStream bos = new BufferedOutputStream(out);
        xwpfDocument.write(bos);
        bos.flush();
        out.flush();
        PoitlIOUtils.closeQuietlyMulti(template, xwpfDocument, bos, out);
        return path;
    }

    @SchemaSwitch(String.class)
    private void processRisk(String operationId, Map<String, Object> model) {
        // 5.3 综合风险研判
        List<TdSpecRiskExcel> riskExcels = getRiskResult(operationId, true);
        model.put("riskResultList", riskExcels);

        // 5.1 风险源识别
        List<String> lifeCycleSort = Lists.newArrayList("网络环境和技术措施", "管理制度和处理流程", "参与人员和获取方管理", "安全态势");
        List<CoVerification> coVerifications = coVerificationMapper.queryVerificationList(operationId, null);
        TdSpecRiskExcel excel = riskExcels.get(0);
        List<Map<String, Object>> riskList = new ArrayList<>();
        // 筛选掉 报批稿 文件
        coVerifications.stream().filter(coVerification -> CharSequenceUtil.isNotEmpty(coVerification.getGpDimension())
            && !coVerification.getBpCode().startsWith("bp.")).collect(Collectors.groupingBy(CoVerification::getStage)).forEach(
            (stage, stageList) -> stageList.stream().sorted(Comparator.comparingDouble(CoVerification::getBpCodeDouble))
                .forEach(coVerification -> {
                Map<String, Object> riskMap = new HashMap<>(16);
                riskMap.put("name", stage);
                riskMap.put("item", coVerification.getStandardProvision());
                riskMap.put("result", coVerification.getResult());
                riskMap.put("systemResult", coVerification.getSystemResult());
                String level = "";
                if (lifeCycleSort.indexOf(coVerification.getStage()) == 0){
                    level = excel.getTechLikeHood();
                } else if (lifeCycleSort.indexOf(coVerification.getStage()) == 1){
                    level = excel.getManageLikeHood();
                } else if (lifeCycleSort.indexOf(coVerification.getStage()) == 2) {
                    level = excel.getRoleLikeHood();
                } else if (lifeCycleSort.indexOf(coVerification.getStage()) == 3){
                    level = excel.getLikeHood();
                }
                riskMap.put("level", level);
                riskList.add(riskMap);
            }));
        model.put("riskList",
            riskList.stream().sorted(Comparator.comparing(map -> lifeCycleSort.indexOf(map.get("name"))))
                .collect(Collectors.toList()));

        List<TreeLabelDTO> treeLabelList = dynamicProcessTreeMapper.selectByOperationIdAndTreeId(operationId, LabelEnum.XTDY.getCode());
        Map<Long, String> busSystemMap = treeLabelList.stream().collect(Collectors.toMap(TreeLabelDTO::getTreeId,TreeLabelDTO::getTreeName));
        // 5.2 风险影响分析
        List<MidThreatResultDTO> threatResults = midThreatResultMapper.listThreatResult(operationId);
        List<Map<String, Object>> impactResultList = new ArrayList<>();
        AtomicInteger sort = new AtomicInteger(1);
        threatResults.forEach(midThreatResult -> {
            Map<String, Object> impactMap = new HashMap<>(16);
            impactMap.put("sort", sort.getAndIncrement());
            impactMap.put("busSystem",  getBusSystem(busSystemMap, String.valueOf(midThreatResult.getSystemId())));
            impactMap.put("type", midThreatResult.getThreatTypeName());
            impactMap.put("impact", midThreatResult.getThreatFrequencyTag());
            impactResultList.add(impactMap);
        });
        model.put("impactList", impactResultList);

        // 5.2.2 定性风险分析
        List<Map<String, Object>> qualitativeRiskList = new ArrayList<>();
        List<AdviseRiskDTO> adviseRisks = adviseRiskMapper.selectRiskContent(operationId);
        AtomicInteger sortBy = new AtomicInteger(1);
        adviseRisks.stream().filter(adviseRiskDTO -> adviseRiskDTO.getRiskLevel() != null).sorted(Comparator.comparingInt(AdviseRiskDTO::getRiskLevel).reversed()).forEach(adviseRiskDTO -> {
            Map<String, Object> map = new HashMap<>();
            map.put("sort", sortBy.getAndIncrement());
            map.put("content", adviseRiskDTO.getContent());
            map.put("riskLevel", adviseRiskDTO.getRiskLevel());
            map.put("describe", adviseRiskDTO.getDescribe());
            map.put("busSystem", getBusSystem(busSystemMap, adviseRiskDTO.getSystemId()));
            qualitativeRiskList.add(map);
        });
        model.put("qualitativeRiskList", qualitativeRiskList);

    }

    private String getBusSystem(Map<Long, String> busSystemMap, String systemId) {
        String[] systemIds = systemId.split(StrPool.COMMA);
        return Arrays.stream(systemIds).map(id -> busSystemMap.get(Long.parseLong(id))).collect(Collectors.joining(StrPool.COMMA));
    }

    @SchemaSwitch(String.class)
    private void lawAssessment(String operationId, Map<String, Object> model) {
        List<ResultCountDTO> resultList = coLegalMapper.queryLegalResult(operationId);
        AtomicBoolean passed = new AtomicBoolean(false);
        AtomicInteger x = new AtomicInteger(0);
        AtomicInteger y = new AtomicInteger(0);
        AtomicInteger z = new AtomicInteger(0);
        // 初始值
        model.put("countA", 0);
        model.put("countB", 0);
        model.put("countC", 0);
        model.put("countD", 0);
        model.put("countE", 0);
        model.put("countF", 0);
        model.put("countG", 0);
        model.put("countH", 0);

        List<Tag> allTagList = tagMapper.selectList(new QueryWrapper<Tag>().eq("type_id",1));
        Map<Long, String> tagMap = allTagList.stream().collect(Collectors.toMap(Tag::getId,Tag::getName));
        resultList.stream().collect(Collectors.groupingBy(ResultCountDTO::getName)).forEach((name, list) -> {
            if ("1 正当必要性评估".equals(name)){
                String result = list.get(0).getResult();
                model.put("resultX", result);
                if (OptEnum.A.getInfo().equals(result)){
                    passed.set(true);
                }
            } else if ("2 基础性安全评估".equals(name)){
                list.forEach(resultCountDTO -> {
                    int count = (resultCountDTO.getCount() == null ? 0 : resultCountDTO.getCount());
                    if (OptEnum.A.getInfo().equals(resultCountDTO.getResult())) {
                        model.put("countA", count);
                        x.addAndGet(count);
                    } else if (OptEnum.B.getInfo().equals(resultCountDTO.getResult()) || OptEnum.E.getInfo().equals(resultCountDTO.getResult())) {
                        model.put("countB", count);
                        y.addAndGet(count);
                    } else if (OptEnum.C.getInfo().equals(resultCountDTO.getResult())) {
                        model.put("countC", count);
                        z.addAndGet(count);
                    } else if (OptEnum.D.getInfo().equals(resultCountDTO.getResult())) {
                        model.put("countD", count);
                    }
                });
            } else if ("3 数据全生命周期安全评估".equals(name)) {
                list.forEach(resultCountDTO -> {
                    int count = (resultCountDTO.getCount() == null ? 0 : resultCountDTO.getCount());
                    if (OptEnum.A.getInfo().equals(resultCountDTO.getResult())) {
                        model.put("countE", count);
                        x.addAndGet(count);
                    } else if (OptEnum.B.getInfo().equals(resultCountDTO.getResult()) || OptEnum.E.getInfo().equals(resultCountDTO.getResult())) {
                        model.put("countF", count);
                        y.addAndGet(count);
                    } else if (OptEnum.C.getInfo().equals(resultCountDTO.getResult())) {
                        model.put("countG", count);
                        z.addAndGet(count);
                    } else if (OptEnum.D.getInfo().equals(resultCountDTO.getResult())) {
                        model.put("countH", count);
                    }
                });
            }
        });
        // 若X为符合项数量，Y为部分符合项数量、Z为不符合项数量，则平均归一化算数分值=（X+0.5Y）/（X+Y+Z）。平均归一化算数分值小于0.7，则判定合规性评估不通过；
        // 平均归一化算数分值大于等于0.7且满足正当必要性，则判定合规性评估通过
        BigDecimal score = (BigDecimal.valueOf(x.get()).add(BigDecimal.valueOf(0.5).multiply(BigDecimal.valueOf(y.get())))).divide(BigDecimal.valueOf(x.get()+y.get()+z.get()), 2, RoundingMode.HALF_UP);
        model.put("score", score);
        if (score.compareTo(BigDecimal.valueOf(0.7)) >= 0 && passed.get()) {
            model.put("resultDesc", "通过");
        } else {
            model.put("resultDesc", "不通过");
        }

        List<String> lawId = coLegalMapper.selectLawIdByOperationId(operationId);
        List<CoLegal> coLegalList = coLegalMapper.selectList(new QueryWrapper<CoLegal>().eq("operation_id", operationId));
        if (CollUtil.isNotEmpty(coLegalList)) {
            // 特殊处理：目前电信专项，电信专项的合规评估，
            //电信专项的合规评估，需要区分一般数据、重要数据和核心数据，COC已经已经打过标签，资产里面如果没有核心数据和重要数据，合规评估里面专门针对的条文就是不适用的，无需评估；资产里面有核心数据或者重要数据，那么合规评估里面就全部都评估
            List<CoInventory> coInventories = coInventoryMapper.selectList(new QueryWrapper<CoInventory>().eq("operation_id", operationId));
            long dataTagCount = coInventories.stream().filter(coInventory -> !DataTagEnum.GENERAL.getTag().equals(coInventory.getDataTag())).count();

            String generalTag;
            if (dataTagCount == 0 ){
                generalTag = tagMapper.selectTagIdByName(DataTagEnum.GENERAL.getDesc());
            } else {
                generalTag = null;
            }
            Map<String, String> itemResultMap =
                coLegalList.stream().collect(Collectors.toMap(CoLegal::getItemId, CoLegal::getResult));
            Map<String, String> descMap =
                coLegalList.stream().filter(coLegal -> CharSequenceUtil.isNotEmpty(coLegal.getDesc())).collect(Collectors.toMap(CoLegal::getItemId, CoLegal::getDesc));
            List<Map<String, Object>> lawChapterList = new ArrayList<>();
//            Map<String, String> descMap = getLawDescribe(operationId);
            List<AdviseRiskDTO> adviseRiskDTOS = adviseRiskMapper.selectLawRiskContentByType(operationId, 1);
            Map<String, String> contentMap = new HashMap<String, String>();
            if (CollUtil.isNotEmpty(adviseRiskDTOS)){
                adviseRiskDTOS.stream().collect(Collectors.groupingBy(AdviseRiskDTO::getItemId)).forEach((k,v)->{
                    String content = v.stream().map(AdviseRiskDTO::getContent).distinct().collect(Collectors.joining(StrUtil.LF));
                    contentMap.put(k, content);
                });
            }
            lawId.forEach(s -> {
                List<LawItem> lawItems = lawItemMapper.sortLawItems(Integer.valueOf(s), generalTag);
                lawItems.stream().filter(lawItem -> !lawItem.getChapter().contains("正当必要性评估"))
                    .collect(Collectors.groupingBy(LawItem::getChapter)).forEach((chapter, list) -> {
                        Map<String, Object> lawChapter = new HashMap<>(16);
                        lawChapter.put("chapter", chapter);
                        List<Map<String, Object>> lawItemList = new ArrayList<>();
                        list.stream().collect(Collectors.groupingBy(LawItem::getSection,TreeMap::new, Collectors.toList())).forEach(
                            (section, sublist) -> sublist.stream().collect(Collectors.groupingBy(LawItem::getTags))
                                .forEach((tags, tagList) -> tagList.forEach(lawItem -> {
                                    Map<String, Object> tag = new HashMap<>(16);
                                    tag.put("section", lawItem.getSection());
                                    tag.put("tags", getTagsCh(tags,tagMap));
                                    tag.put("item", lawItem.getContent());
                                    tag.put("desc", descMap.get(lawItem.getId()));
                                    tag.put("result", itemResultMap.get(lawItem.getId()));
                                    tag.put("content", contentMap.get(lawItem.getId()));
                                    lawItemList.add(tag);
                                })));
                        lawChapter.put("lawItems", lawItemList);
                        lawChapterList.add(lawChapter);
                    });
            });
            model.put("lawChapterList", lawChapterList);
        }
    }

    private Object getTagsCh(String tags, Map<Long, String> tagMap) {
        return Arrays.stream(tags.split(StrPool.COMMA)).map(tag -> tagMap.get(Long.parseLong(tag))).collect(Collectors.joining(StrPool.COMMA));
    }

    @Override
    public ReportTypeEnum getType() {
        return ReportTypeEnum.TD;
    }
}
