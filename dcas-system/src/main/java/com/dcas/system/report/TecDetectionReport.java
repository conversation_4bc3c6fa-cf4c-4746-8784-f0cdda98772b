package com.dcas.system.report;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.domain.entity.*;
import com.dcas.common.enums.AbilityType;
import com.dcas.common.enums.DataSourceType;
import com.dcas.common.enums.LabelEnum;
import com.dcas.common.mapper.*;
import com.dcas.common.model.dto.*;
import com.dcas.common.model.vo.*;
import com.dcas.common.utils.SecurityUtils;
import com.dcas.common.utils.spring.SpringUtils;
import com.dcas.market.app.service.IAppService;
import com.dcas.system.service.CoViewCustomerService;
import com.dcas.system.service.ScanTaskService;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.deepoove.poi.plugin.toc.TOCRenderPolicy;
import com.deepoove.poi.util.PoitlIOUtils;
import com.deepoove.poi.xwpf.NiceXWPFDocument;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @className TecDetectionReport
 * @description 技术检测报告
 * @date 2024/11/11 14:09
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TecDetectionReport extends AbstractReport implements ReportInterface{

    private final CoInventoryMapper coInventoryMapper;
    private final PreSourceConfigMapper preSourceConfigMapper;
    private final IAppService iAppService;
    private final CoViewCustomerService coViewCustomerService;
    private final CoPermissionMapper coPermissionMapper;
    private final DynamicProcessTreeMapper dynamicProcessTreeMapper;

    @Value("${safety.profile}")
    private String basePath;
    @Override
    public ReportTypeEnum getType() {
        return ReportTypeEnum.TEC_DETECTION;
    }

    @Override
    public String exportWord(ExportWordDto dto, QueryProjectOperationExportVo poVo) throws Exception {
        //获取模板地址，注意k8s无法识别中文文件名，中文文件失效
        ClassPathResource classPathResource = new ClassPathResource("template/TecDetectionReportTemplate.docx");
        InputStream inputStream = classPathResource.getInputStream();

        //数据模型
        Map<String, Object> model = new HashMap<>(16);

        //准备数据
        String operationId = dto.getOperationId();
        List<ExportWordChart> chartList = dto.getChartList();

        RequestModel<OperationIdDto> requestModel = new RequestModel<>();
        OperationIdDto operationIdDto = new OperationIdDto();
        operationIdDto.setOperationId(operationId);
        requestModel.setPrivator(operationIdDto);

        //评估内容
        List<Long> serviceContentList = JSON.parseArray("[" + poVo.getServiceContent() + "]", Long.class);

        // 将检测能力添加到评估内容列表中去
        List<PreSourceConfig> preSourceConfigList = preSourceConfigMapper.selectList(new QueryWrapper<PreSourceConfig>().eq("operation_id", operationId));
        for (PreSourceConfig preSourceConfig : preSourceConfigList) {
            if (AbilityType.contains(preSourceConfig.getAbilityModule(),
                AbilityType.DESENSITIZATION) && !serviceContentList.contains(
                Long.valueOf(AbilityType.DESENSITIZATION.getCode()))) {
                serviceContentList.add(Long.valueOf(AbilityType.DESENSITIZATION.getCode()));
            }
            if (AbilityType.contains(preSourceConfig.getAbilityModule(),
                AbilityType.ENCRYPT) && !serviceContentList.contains(Long.valueOf(AbilityType.ENCRYPT.getCode()))) {
                serviceContentList.add(Long.valueOf(AbilityType.ENCRYPT.getCode()));
            }
            if (AbilityType.contains(preSourceConfig.getAbilityModule(),
                AbilityType.PCAP) && !serviceContentList.contains(Long.valueOf(AbilityType.PCAP.getCode()))) {
                serviceContentList.add(Long.valueOf(AbilityType.PCAP.getCode()));
            }
            if (AbilityType.contains(preSourceConfig.getAbilityModule(),
                    AbilityType.AUTHORITY) && !serviceContentList.contains(Long.valueOf(AbilityType.AUTHORITY.getCode()))) {
                serviceContentList.add(Long.valueOf(AbilityType.AUTHORITY.getCode()));
            }
        }

        // 判断是否包含这些评估内容，不存在则直接返回
        if (!serviceContentList.contains(LabelEnum.JCHJ.getCode())
            && !serviceContentList.contains(LabelEnum.SJQX.getCode())
            && !serviceContentList.contains(LabelEnum.ZCPD.getCode())
            && !serviceContentList.contains(Long.valueOf(AbilityType.DESENSITIZATION.getCode()))
            && !serviceContentList.contains(Long.valueOf(AbilityType.PCAP.getCode()))
            && !serviceContentList.contains(Long.valueOf(AbilityType.ENCRYPT.getCode()))
            && !serviceContentList.contains(Long.valueOf(AbilityType.AUTHORITY.getCode()))) {
            return null;
        }
        // 公共部分
        putCommonInfo(poVo, model);
        //查询资产盘点表
        QueryWrapper<CoInventory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", dto.getOperationId());
        queryWrapper.orderByDesc("bus_system");
        List<CoInventory> coInventoryList = coInventoryMapper.selectList(queryWrapper);
        List<String> coInventories =
            coInventoryList.stream().map(CoInventory::getBusSystem).distinct().collect(Collectors.toList());
        putPicture(model, chartList);
        model.put("securityTech", false);
        // 网络资产分析
        pcapAnalysis(chartList, model, serviceContentList, operationId);
        // 资产分析以及权限分析
        assetAnalysis(chartList, requestModel, model, coInventories, coInventoryList, serviceContentList);
        // 基础评估
        baseAssessment(model, chartList, serviceContentList, dto);
        // 脱敏加密检测分析
        compareAnalysis(model, serviceContentList, operationId);
        // 数据权限访问控制
        authorityAccess(model, serviceContentList, operationId);
        // 安全能力验证
        securityCapability(model, poVo.getSubAbility(), operationId);
        // 总结与评价
        buildSummaryAndEvaluation(model);

        //配置模板
        LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
        TOCRenderPolicy tocPolicy = new TOCRenderPolicy();
        Configure config =
            Configure.builder()
                .bind("userAssetAuthorityList", policy).bind("busSystemAuthorityList", policy)
                .bind("detectionRecordList", policy)
                .bind("deviceList", policy)
                .bind("dmList", policy)
                .bind("epList", policy)
                .bind("dmDiffList", policy)
                .bind("epDiffList", policy)
                .bind("dtList", policy)
                .bind("isList", policy)
                .bind("tocContents", tocPolicy)
                .bind("authList", policy)
                .bind("classifyList", policy)
                .bind("othersCapabilityList", policy)
                .build();

        //加载模板渲染数据
        XWPFTemplate template = XWPFTemplate.compile(inputStream, config).render(model);
        NiceXWPFDocument xwpfDocument = template.getXWPFDocument();
        xwpfDocument.enforceUpdateFields();

        String realFileName = String.format("%s技术检测报告.docx", poVo.getOperationName());
        String path = String.join(File.separator, basePath, "temp", realFileName);
        FileUtil.touch(path);

        //输出结果
        OutputStream out = Files.newOutputStream(Paths.get(path));
        BufferedOutputStream bos = new BufferedOutputStream(out);
        xwpfDocument.write(bos);
        bos.flush();
        out.flush();
        PoitlIOUtils.closeQuietlyMulti(template, xwpfDocument, bos, out);
        return path;
    }

    private void buildSummaryAndEvaluation(Map<String, Object> model) {
        List<String> list = new ArrayList<>();
        if (model.get("pcap") != null && (boolean) model.get("pcap")) {
            list.add("网络资产发现");
        }
        if (model.get("assetsCheck") != null && (boolean) model.get("assetsCheck")) {
            list.add("数据资产梳理");
        }
        if (model.get("dataAuthority") != null && (boolean) model.get("dataAuthority")) {
            list.add("数据权限检测");
        }
        if (model.get("baseAssessment") != null && (boolean) model.get("baseAssessment")) {
            list.add("基础评估检测");
        }
        if (model.get("securityCapability") != null && (boolean) model.get("securityCapability")) {
            list.add("安全能力验证");
        }
        if (model.get("securityTech") != null && (boolean) model.get("securityTech")) {
            list.add("安全技术检测");
        }
        model.put("serviceContents", CollUtil.join(list, "、"));
    }

    private void securityCapability(Map<String, Object> model, String subAbility, String operationId) {
        model.put("securityCapability", false);
        model.put("classify", false);
        model.put("othersCapability", false);
        if (StrUtil.isNotEmpty(subAbility)) {
            model.put("securityCapability", true);
            List<CapabilityReportDTO> capabilityReportList = iAppService.selectCapabilityReport(operationId);
            if (CollUtil.isNotEmpty(capabilityReportList)) {
                Map<Long, String> systemMap = dynamicProcessTreeMapper.selectByOperationIdAndTreeId(operationId, LabelEnum.XTDY.getCode())
                        .stream().collect(Collectors.toMap(TreeLabelDTO::getTreeId, TreeLabelDTO::getTreeName));
                List<PreSourceConfig> sourceConfigList = preSourceConfigMapper.selectList(new QueryWrapper<PreSourceConfig>().eq("operation_id", operationId));
                List<Map<String, Object>> classifyList = new ArrayList<>();
                List<Map<String, Object>> othersCapabilityList = new ArrayList<>();
                Map<Integer, AtomicInteger> sortMap = new HashMap<>();
                sortMap.put(1, new AtomicInteger(1));
                sortMap.put(2, new AtomicInteger(1));
                for (CapabilityReportDTO dto : capabilityReportList) {
                    Map<String, Object> map = new HashMap<>();
                    if (StrUtil.contains(dto.getCapability(), "分类分级")) {
                        AtomicInteger atomicInteger = sortMap.get(1);
                        map.put("sort", atomicInteger.getAndIncrement());
                        map.put("bsName", systemMap.get(dto.getSystemId()));
                        if (CollUtil.isNotEmpty(sourceConfigList)) {
                            for (PreSourceConfig preSourceConfig : sourceConfigList) {
                                if (StrUtil.contains(preSourceConfig.getAppIds(), String.valueOf(dto.getId()))) {
                                    map.put("dbName", preSourceConfig.getConfigName());
                                    break;
                                }
                            }
                        }
                        map.put("result", dto.getVerifyResult());
                        classifyList.add(map);
                    } else {
                        AtomicInteger atomicInteger = sortMap.get(2);
                        map.put("sort", atomicInteger.getAndIncrement());
                        map.put("capability", dto.getCapability());
                        map.put("bsName", systemMap.get(dto.getSystemId()));
                        map.put("productName", dto.getProductName());
                        map.put("result", dto.getVerifyResult());
                        othersCapabilityList.add(map);
                    }
                }
                Map<Long, List<CapabilityReportDTO>> systemCapabilityGroup = capabilityReportList.stream().collect(Collectors.groupingBy(CapabilityReportDTO::getSystemId));
                StringBuilder sb = new StringBuilder();
                systemCapabilityGroup.forEach((k, v) -> {
                    sb.append(systemMap.get(k));
                    // 具备列表
                    Set<String> hasSet = new HashSet<>();
                    // 不具备列表
                    Set<String> noSet = new HashSet<>();
                    v.forEach(dto -> {
                        if ("具备".equals(dto.getVerifyResult())) {
                            hasSet.add(dto.getCapability());
                        } else {
                            noSet.add(dto.getCapability());
                        }
                    });
                    if (CollUtil.isNotEmpty(hasSet)) {
                        sb.append("具备").append(CollUtil.join(hasSet, StrUtil.COMMA));
                    }
                    if (CollUtil.isNotEmpty(noSet)) {
                        if (CollUtil.isNotEmpty(hasSet))
                            sb.append("，");
                        sb.append("不具备").append(CollUtil.join(noSet, StrUtil.COMMA));
                    }
                    sb.append("；");
                });
                // 截取掉最后一个分号
                model.put("capabilityResult", sb.deleteCharAt(sb.lastIndexOf("；")).toString());
                if (CollUtil.isNotEmpty(classifyList)) {
                    model.put("classify", true);
                    model.put("classifyList", classifyList);
                }
                if (CollUtil.isNotEmpty(othersCapabilityList)) {
                    model.put("othersCapability", true);
                    model.put("othersCapabilityList", othersCapabilityList);
                }
            }
        }
    }

    private void authorityAccess(Map<String, Object> model, List<Long> serviceContentList, String operationId) {
        model.put("auth", false);
        if (serviceContentList.contains(Long.valueOf(AbilityType.AUTHORITY.getCode()))) {
            model.put("auth", true);
            model.put("securityTech", true);
            List<CoPermissionDetail> details = coPermissionMapper.queryByOperationId(operationId);
            AtomicInteger sort = new AtomicInteger(1);
            List<PermissionReportDTO> authList = details.stream().map(d -> {
                PermissionReportDTO dto = new PermissionReportDTO();
                dto.setSort(sort.getAndIncrement());
                dto.setConfigType(DataSourceType.getType(d.getConfigType()).getName());
                dto.setHost(SecurityUtils.decryptAes(d.getHost()));
                dto.setUsername(SecurityUtils.decryptAes(d.getUsername()));
                dto.setResult(Objects.equals(d.getStatus(), Boolean.TRUE) ? "不具备访问控制" : "具备访问控制");
                return dto;
            }).collect(Collectors.toList());
            model.put("authList", authList);
            model.put("accountNum", details.size());
            long count = details.stream().filter(d -> Objects.equals(d.getStatus(), false)).count();
            model.put("partNum", count);
            double percent = details.isEmpty() ? 0 : NumberUtil.div(count * 100, details.size(), 1);
            model.put("percent", percent);
            double hasPercent = details.isEmpty() ? 0 : NumberUtil.div((details.size() - count) * 100, details.size(), 1);
            model.put("possessAccount", hasPercent);
            String level;
            if (percent > 60)
                level = "较高";
            else if (percent > 30) {
                level = "中等";
            } else
                level = "较低";
            model.put("level", level);
        }
    }

    private void putPicture(Map<String, Object> model, List<ExportWordChart> chartList) {
        //敏感数据占比图表 从入参中获取
        List<ExportWordChart> userAddChartList =
            chartList.stream().filter(s -> "用户权限分类统计结果(新增)".equals(s.getName())).collect(Collectors.toList());
        List<ExportWordChart> userDeleteDataChartList =
            chartList.stream().filter(s -> "用户权限分类统计结果(删除数据)".equals(s.getName())).collect(Collectors.toList());
        List<ExportWordChart> userDeleteTableChartList =
            chartList.stream().filter(s -> "用户权限分类统计结果(删除表)".equals(s.getName())).collect(Collectors.toList());
        List<ExportWordChart> userUpdateChartList =
            chartList.stream().filter(s -> "用户权限分类统计结果(修改)".equals(s.getName())).collect(Collectors.toList());
        List<ExportWordChart> userQueryChartList =
            chartList.stream().filter(s -> "用户权限分类统计结果(查询)".equals(s.getName())).collect(Collectors.toList());
        // 设备类型饼图
        List<ExportWordChart> deviceAsset =
            chartList.stream().filter(s -> "设备占比排序".equals(s.getName())).collect(Collectors.toList());

        Map<String, String> imageBase64DeviceAsset = getPicture(deviceAsset);
        Map<String, String> imageBase64Data2 = getPicture(userAddChartList);
        Map<String, String> imageBase64Data3 = getPicture(userDeleteDataChartList);
        Map<String, String> imageBase64Data4 = getPicture(userDeleteTableChartList);
        Map<String, String> imageBase64Data5 = getPicture(userUpdateChartList);
        Map<String, String> imageBase64Data6 = getPicture(userQueryChartList);
        putImage("userAuthorityTypeAddImg", imageBase64Data2, model);
        putImage("userAuthorityTypeDeleteImg", imageBase64Data3, model);
        putImage("userAuthorityTypeDropImg", imageBase64Data4, model);
        putImage("userAuthorityTypeUpdateImg", imageBase64Data5, model);
        putImage("userAuthorityTypeSelectImg", imageBase64Data6, model);
        putImage("deviceImg", imageBase64DeviceAsset, model);
    }

    private void compareAnalysis(Map<String, Object> model, List<Long> serviceContentList, String operationId) {
        model.put("ep", false);
        if (serviceContentList.contains(Long.valueOf(AbilityType.ENCRYPT.getCode()))) {
            List<ApiCompareVO> apiCompareList = iAppService.compareReportQuery(operationId, AbilityType.ENCRYPT.getCode());
            model.put("ep", true);
            model.put("securityTech", true);
            List<Map<String, Object>> epDiffList = new ArrayList<>();
            if (CollUtil.isEmpty(apiCompareList)){
                return;
            }
            Map<String, AtomicInteger> systemReachMap = new HashMap<>(16);
            apiCompareList.forEach(apiCompareVO -> {
                apiCompareVO.setResult(convertResult(apiCompareVO.getResult()));
                if (systemReachMap.containsKey(apiCompareVO.getBusSystem())){
                    AtomicInteger count = systemReachMap.get(apiCompareVO.getBusSystem());
                    if (!"一致".equals(apiCompareVO.getResult())){
                        count.incrementAndGet();
                    }
                    systemReachMap.put(apiCompareVO.getBusSystem(), count);
                } else {
                    AtomicInteger total = new AtomicInteger(0);
                    if (!"一致".equals(apiCompareVO.getResult())){
                        total.incrementAndGet();
                    }
                    systemReachMap.put(apiCompareVO.getBusSystem(), total);
                }
                apiCompareVO.getTableDetailList().forEach(tableDetail ->
                    tableDetail.getSampleDetailList().forEach(sampleDetail -> {
                        Map<String, Object> diffMap = new HashMap<>(16);
                        diffMap.put("sort", apiCompareVO.getSort());
                        diffMap.put("busSystem", apiCompareVO.getBusSystem());
                        diffMap.put("dbName", apiCompareVO.getDbName());
                        diffMap.put("sourceTable", tableDetail.getSourceTable());
                        diffMap.put("sourceColumn", sampleDetail.getSourceColumn());
                        diffMap.put("targetTable", tableDetail.getTargetTable());
                        diffMap.put("targetColumn", sampleDetail.getTargetColumn());
                        diffMap.put("result", sampleDetail.getResult());
                        epDiffList.add(diffMap);
                    }));
            });
            int reachCount = 0;
            for (Map.Entry<String, AtomicInteger> entry : systemReachMap.entrySet()){
                if (entry.getValue().get() > 0 ){
                    reachCount++;
                }
            }
            model.put("epList", apiCompareList);
            model.put("epDiffList", epDiffList);
            model.put("epTotal", reachCount);
            double epReachRate = BigDecimal.valueOf(reachCount)
                .divide(BigDecimal.valueOf(systemReachMap.size()), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100)).doubleValue();
            model.put("epReachRate", epReachRate);
            model.put("epLevel", convertLevel(epReachRate));
        }

        model.put("dm", false);
        if (serviceContentList.contains(Long.valueOf(AbilityType.DESENSITIZATION.getCode()))) {
            List<ApiCompareVO>  apiCompareList = iAppService.compareReportQuery(operationId, AbilityType.DESENSITIZATION.getCode());
            model.put("dm", true);
            model.put("securityTech", true);
            List<Map<String, Object>> dmDiffList = new ArrayList<>();
            Map<String, AtomicInteger> systemReachMap = new HashMap<>(16);
            apiCompareList.forEach(apiCompareVO -> {
                apiCompareVO.setResult(convertResult(apiCompareVO.getResult()));
                if (systemReachMap.containsKey(apiCompareVO.getBusSystem())){
                    AtomicInteger count = systemReachMap.get(apiCompareVO.getBusSystem());
                    if (!"一致".equals(apiCompareVO.getResult())){
                        count.incrementAndGet();
                    }
                    systemReachMap.put(apiCompareVO.getBusSystem(), count);
                } else {
                    AtomicInteger total = new AtomicInteger(0);
                    if (!"一致".equals(apiCompareVO.getResult())){
                        total.incrementAndGet();
                    }
                    systemReachMap.put(apiCompareVO.getBusSystem(), total);
                }
                apiCompareVO.getTableDetailList().forEach(tableDetail ->
                    tableDetail.getSampleDetailList().forEach(sampleDetail -> {
                        Map<String, Object> diffMap = new HashMap<>(16);
                        diffMap.put("sort", apiCompareVO.getSort());
                        diffMap.put("busSystem", apiCompareVO.getBusSystem());
                        diffMap.put("dbName", apiCompareVO.getDbName());
                        diffMap.put("sourceTable", tableDetail.getSourceTable());
                        diffMap.put("sourceColumn", sampleDetail.getSourceColumn());
                        diffMap.put("targetTable", tableDetail.getTargetTable());
                        diffMap.put("targetColumn", sampleDetail.getTargetColumn());
                        diffMap.put("result", sampleDetail.getResult());
                        dmDiffList.add(diffMap);
                    }));
            });
            int reachCount = 0;
            for (Map.Entry<String, AtomicInteger> entry : systemReachMap.entrySet()){
                if (entry.getValue().get() > 0 ){
                    reachCount++;
                }
            }
            model.put("dmList", apiCompareList);
            model.put("dmDiffList", dmDiffList);
            model.put("dmTotal", reachCount);
            double dmReachRate = BigDecimal.valueOf(reachCount)
                .divide(BigDecimal.valueOf(systemReachMap.size()), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100)).doubleValue();
            model.put("dmReachRate", dmReachRate);
            model.put("dmLevel", convertLevel(dmReachRate));
        }
    }

    private String convertResult(String result) {
        if ("一致".equals(result)){
            return "未实现数据脱敏";
        } else if ("部分一致".equals(result)){
            return "部分实现数据脱敏";
        } else {
            return "完全实现数据脱敏";
        }
    }

    private String convertLevel(double epReachRate) {
        if (epReachRate>= 0 && epReachRate <= 70){
            return "较高";
        } else {
            return "较低";
        }
    }

    private void pcapAnalysis(List<ExportWordChart> chartList, Map<String, Object> model, List<Long> serviceContentList,
        String operationId) {
        model.put("pcap", false);
        if (serviceContentList.contains(Long.valueOf(AbilityType.PCAP.getCode()))) {
            model.put("pcap", true);
            model.put("securityTech", true);
            ApiNetworkAssetVO vo = iAppService.pcapReportQuery(operationId);
            if (vo == null){
                return;
            }
            List<Map<String, Object>> deviceList = new ArrayList<>();
            List<Map<String, Object>> dtList = new ArrayList<>();
            model.put("total", vo.getAssetTotalNum());
            model.put("typeCount", vo.getAssetDetailList().size());

            AtomicInteger sort = new AtomicInteger(1);
            vo.getAssetDetailList().stream().sorted(Comparator.comparing(ApiNetworkAssetVO.AssetDetail::getAssetNum).reversed()).forEach(assetDetail -> {
                Map<String, Object> typeMap = new HashMap<>(16);
                typeMap.put("sort", sort.getAndIncrement());
                typeMap.put("deviceType", assetDetail.getDeviceType());
                typeMap.put("assetNum", assetDetail.getAssetNum());
                deviceList.add(typeMap);
            });
            model.put("deviceList", deviceList);

            // 5.3数据传输加密技术检测
            List<ApiInterfaceVO> apiInterfaces = coViewCustomerService.jobInterfaceReport(operationId);
            apiInterfaces.forEach(apiInterfaceVO -> {
                Map<String, Object> dtMap = new HashMap<>(16);
                dtMap.put("sort", sort.getAndIncrement());
                dtMap.put("interface", apiInterfaceVO.getName());
                dtMap.put("url", apiInterfaceVO.getUrl());
                dtMap.put("name", apiInterfaceVO.getAssetName());
                dtMap.put("protocol", apiInterfaceVO.getProtocol());
                dtMap.put("method", apiInterfaceVO.getMethod());
                dtMap.put("contentType", apiInterfaceVO.getContentType());
                dtList.add(dtMap);
            });
            model.put("dtList", dtList);
            model.put("isList", dtList);
            model.put("totalInterface", dtList.size());
            model.put("riskDesc", !dtList.isEmpty() ? "存在一定风险" : "风险较低");
        }
    }

    @Override
    protected void baseAssessment(Map<String, Object> model, List<ExportWordChart> chartList,
        List<Long> serviceContentList, ExportWordDto privator) {
        DetectionResultMapper detectionResultMapper = SpringUtils.getBean(DetectionResultMapper.class);
        CoOperationMapper coOperationMapper = SpringUtils.getBean(CoOperationMapper.class);
        ScanTaskService scanTaskService = SpringUtils.getBean(ScanTaskService.class);

        // 5.1基础评估
        model.put("baseAssessment", false);
        if (serviceContentList.contains(LabelEnum.JCHJ.getCode()) && !privator.getNeedExcludeContent()
            .contains(LabelEnum.JCHJ)) {
            model.put("baseAssessment", true);

            List<DetectionResult> detectionResults = detectionResultMapper
                .selectList(new QueryWrapper<DetectionResult>().eq("operation_id", privator.getOperationId()));
            Set<String> dbTypeSet = new HashSet<>();
            Map<String, List<DetectionResult>> dbConfigMap = new HashMap<>(16);
            int successCount = 0;
            int failCount = 0;
            for (DetectionResult detectionResult : detectionResults) {
                dbTypeSet.add(DataSourceType.getType(detectionResult.getDbType()).getName());
                if (dbConfigMap.containsKey(detectionResult.getDbConfig())) {
                    dbConfigMap.get(detectionResult.getDbConfig()).add(detectionResult);
                } else {
                    List<DetectionResult> list = new ArrayList<>();
                    list.add(detectionResult);
                    dbConfigMap.put(detectionResult.getDbConfig(), list);
                }
                if (detectionResult.getAccord()) {
                    successCount++;
                } else {
                    failCount++;
                }
            }

            model.put("dbTypeStr", String.join("、", dbTypeSet));
            model.put("dbCount", dbConfigMap.size());
            model.put("totalCount", detectionResults.size());
            model.put("successCount", successCount);
            model.put("failCount", failCount);
            BigDecimal rate = BigDecimal.valueOf(successCount)
                .divide(BigDecimal.valueOf(detectionResults.size()), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
            model.put("successRate", rate.setScale(2, RoundingMode.HALF_UP));

            //整体检测记录
            //1、2阶段查询
            QueryProjectOperationExportVo poVo = coOperationMapper.queryOperationExport(privator.getOperationId());
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            List<Map<String, Object>> detectionRecordList = new ArrayList<>();
            List<String> projectManagerList = StrUtil.split(poVo.getProjectManager(), StrUtil.C_COMMA);
            List<String> executorList = StrUtil.split(poVo.getExecutorAccount(), StrUtil.C_COMMA);
            List<String> reviewerList = StrUtil.split(poVo.getReviewerAccount(), StrUtil.C_COMMA);
            model.put("executor",
                String.join("、", CollUtil.unionDistinct(projectManagerList, executorList, reviewerList)));
            model.put("createTime", simpleDateFormat.format(poVo.getCreateTime()));

            // 数据库检测详情
            List<Map<String, Object>> dbSourceList = new ArrayList<>();

            AtomicInteger sort = new AtomicInteger();
            dbConfigMap.forEach((k, v) -> {

                Map<String, Object> recordMap = new HashMap<>(16);
                DetectionResult detectionResult = v.get(0);

                SourceConfig sourceConfig =
                    com.alibaba.fastjson.JSON.parseObject(detectionResult.getDbConfig(), SourceConfig.class);
                String dbType = DataSourceType.getType(v.get(0).getDbType()).getName();
                long count = v.stream().filter(DetectionResult::getAccord).count();
                int total = v.size();
                recordMap.put("sort", sort.incrementAndGet());
                recordMap.put("dbName", sourceConfig == null ? null : sourceConfig.getConfigName());
                recordMap.put("dbType", dbType);
                recordMap.put("pointCount", total);
                BigDecimal pointRate =
                    BigDecimal.valueOf(count).divide(BigDecimal.valueOf(total), 4, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(100));
                recordMap.put("pointRate", pointRate.setScale(2, RoundingMode.HALF_UP));
                detectionRecordList.add(recordMap);

                Map<String, Object> dbSourceInfo = new HashMap<>(16);
                dbSourceInfo.put("dbSourceName", sourceConfig == null ? null : sourceConfig.getConfigName());
                List<ExportWordChart> chartList2 = chartList.stream()
                    .filter(s -> String.format("基础评估分析-%s数据源检测结果图", sourceConfig.getConfigName()).equals(s.getName()))
                    .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(chartList2)) {
                    dbSourceInfo.put("dbSourceAccordImg", getPictureStream(getPicture(chartList2)));
                }
                dbSourceInfo.put("dbType", DataSourceType.getType(detectionResult.getDbType()).getName());
                dbSourceInfo.put("ip", sourceConfig == null ? null : sourceConfig.getHost());
                dbSourceInfo.put("port", sourceConfig == null ? null : sourceConfig.getPort());
                dbSourceInfo.put("dbName", sourceConfig == null ? null : sourceConfig.getDbName());
                dbSourceInfo.put("total", total);
                dbSourceInfo.put("sCount", count);
                dbSourceInfo.put("fCount", total - count);
                dbSourceInfo.put("rate", recordMap.get("pointRate"));

                // 检查项详情
                List<Map<String, Object>> detectionDetails = new ArrayList<>();
                Map<String, List<DetectionResult>> optionMap =
                    v.stream().filter(result -> StrUtil.isNotEmpty(result.getOption()))
                        .collect(Collectors.groupingBy(DetectionResult::getOption));
                optionMap.forEach((optionName, pointList)->{
                    Map<String, Object> optionInfo = new HashMap<>(16);
                    optionInfo.put("detectionName", optionName);
                    // 检查点详情
                    List<Map<String, Object>> detectionPointDetails = new ArrayList<>();
                    pointList.forEach(point -> {
                        Map<String, Object> pointInfo = new HashMap<>(16);
                        pointInfo.put("detectionPoint", point.getContent());
                        pointInfo.put("detectionResult", point.getAccord() ? "符合" : "不符合");
                        pointInfo.put("dbType", DataSourceType.getType(point.getDbType()).getName());
                        pointInfo.put("desc", point.getDescribe());
                        if (StrUtil.isNotEmpty(point.getResult())) {
                            JSONObject jsonObject = com.alibaba.fastjson.JSON.parseObject(point.getResult());
                            JSONArray jsonArray = jsonObject.getJSONArray("data");
                            StringBuilder sb = new StringBuilder();
                            jsonArray.forEach(o -> {
                                ((JSONObject)o)
                                    .forEach((key, value) -> sb.append(key).append(":").append(value).append("\n"));
                            });
                            pointInfo.put("result", sb.length() > 0 ? sb.deleteCharAt(sb.lastIndexOf("\n")): sb);
                        }
                        // 不符合的情况下需展示建议措施
                        if (Boolean.FALSE.equals(point.getAccord())) {
                            pointInfo.put("suggest", point.getUnqualified());
                        }
                        detectionPointDetails.add(pointInfo);
                    });
                    optionInfo.put("detectionPointDetails", detectionPointDetails);
                    detectionDetails.add(optionInfo);
                });
                dbSourceInfo.put("detectionDetails", detectionDetails);
                dbSourceList.add(dbSourceInfo);
            });
            model.put("detectionRecordList", detectionRecordList);
            model.put("dbSourceList", dbSourceList);

            List<ExportWordChart> chartList1 =
                chartList.stream().filter(s -> "基础评估分析-数据库安全基线达标率分布图".equals(s.getName())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(chartList1)) {
                model.put("dbAccordRateImg", getPictureStream(getPicture(chartList1)));
            }

            model.put("scanTaskExist", false);
            List<ScanTaskVO> scanTaskList = scanTaskService.queryScanTask(poVo.getOperationId());
            if (CollUtil.isNotEmpty(scanTaskList)){
                int pocNum = 0;
                int highNum = 0;
                int midNum = 0;
                int lowNum = 0;
                int pwNum = 0;
                List<Map<String, Object>> scanRiskList = new ArrayList<>();
                Map<String, Object> hostRisk = new HashMap<>(16);
                int hostSort = 1;
                for (ScanTaskVO scanTaskVO : scanTaskList){
                    hostRisk.put("sort", hostSort++);
                    ScanTaskInfoVO scanTaskInfoVO = scanTaskService.queryScanTaskDetail(scanTaskVO.getTaskId());
                    for (ScanTaskInfoVO.Task task : scanTaskInfoVO.getTask()){
                        pocNum += task.getPocRisk();
                        highNum += task.getHighRisk();
                        midNum += task.getMiddleRisk();
                        lowNum += task.getLowRisk();
                        pwNum += task.getPwNum();
                        hostRisk.put("host", task.getIp());
                        hostRisk.put("riskNum", String
                            .format("可入侵漏洞%d个,高风险漏洞%d个,中风险漏洞%d个,低风险漏洞%d个,弱口令%d个", task.getPocRisk(), task.getHighRisk(),
                                task.getMiddleRisk(), task.getLowRisk(), task.getPwNum()));
                    }
                    scanRiskList.add(hostRisk);
                }
                model.put("scanTaskExist", true);
                model.put("scanRiskList", scanRiskList);
                model.put("hostNum", scanTaskList.size());
                model.put("pocNum", pocNum);
                model.put("highNum", highNum);
                model.put("midNum", midNum);
                model.put("lowNum", lowNum);
                model.put("pwNum", pwNum);
            }
        }
    }
}
