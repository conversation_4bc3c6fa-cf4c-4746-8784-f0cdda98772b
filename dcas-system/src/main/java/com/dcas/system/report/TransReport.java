package com.dcas.system.report;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.NumberChineseFormatter;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.domain.entity.*;
import com.dcas.common.enums.FileType;
import com.dcas.common.enums.LabelEnum;
import com.dcas.common.enums.LegalModelEnum;
import com.dcas.common.enums.OptEnum;
import com.dcas.common.mapper.*;
import com.dcas.common.model.dto.*;
import com.dcas.common.model.vo.FormConfigTreeVO;
import com.dcas.common.model.vo.QueryProjectOperationExportVo;
import com.dcas.common.model.vo.QueryViewLegalResultVo;
import com.dcas.common.model.vo.RiskAnalysisReportVO;
import com.dcas.common.utils.StringUtils;
import com.dcas.system.report.attachment.AttachmentReportFactory;
import com.dcas.system.service.IRiskAnalysisService;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.deepoove.poi.plugin.toc.TOCRenderPolicy;
import com.deepoove.poi.util.PoitlIOUtils;
import com.deepoove.poi.xwpf.NiceXWPFDocument;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.*;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p>
 *     交通运输数据安全风险评估报告
 * </p>
 *
 * <AUTHOR>
 * @date 2025/7/24 15:13
 * @since 2.1.3.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TransReport extends AbstractReport implements ReportInterface {

    private final CoFileMapper coFileMapper;
    private final CoLegalMapper coLegalMapper;
    private final LawItemMapper lawItemMapper;
    private final ModelFileMapper modelFileMapper;
    private final CoProgressMapper coProgressMapper;
    private final AdviseRiskMapper adviseRiskMapper;
    private final CoGapAnalysisMapper coGapAnalysisMapper;
    private final CoVerificationMapper coVerificationMapper;
    private final IRiskAnalysisService iRiskAnalysisService;
    private final MidThreatResultMapper midThreatResultMapper;
    private final DynamicProcessTreeMapper dynamicProcessTreeMapper;
    private final CoModelAnalysisResultMapper coModelAnalysisResultMapper;
    private final QuestionnaireContentMapper questionnaireContentMapper;

    @Value("${safety.profile}")
    private String basePath;

    @Override
    public ReportTypeEnum getType() {
        return ReportTypeEnum.TRANS;
    }

    @Override
    public String exportWord(ExportWordDto dto, QueryProjectOperationExportVo poVo, Long modelId) throws Exception {
        List<String> filePathList = new ArrayList<>();
        CompletableFuture<String> future = CompletableFuture.allOf(CompletableFuture.supplyAsync(() -> {
            try {
                return process(dto, poVo, modelId);
            } catch (IOException e) {
                log.error("导出报告失败", e);
                return null;
            }
        }).whenComplete((v, th) -> {
            if (th != null) {
                log.error("", th);
            }
            if (v != null) {
                log.info(v);
                filePathList.add(v);
            }
        }), CompletableFuture.supplyAsync(() -> {
            try {
                return AttachmentReportFactory.getAssetReportHandler(ReportTypeEnum.COMMON).exportWord( dto,
                        poVo);
            } catch (Exception e) {
                log.error("导出资产清单附件失败", e);
                return null;
            }
        }).whenComplete((v, th) -> {
            if (th != null) {
                log.error("", th);
            }
            if (v != null) {
                log.info(v);
                filePathList.add(v);
            }
        }), CompletableFuture.supplyAsync(() -> {
                    try {
                        dto.setNeedExcludeContent(getNeedExcludeContent());
                        return  ReportFactory.getReportHandler(ReportTypeEnum.TEC_DETECTION).exportWord(dto, poVo);
                    } catch (Exception e) {
                        log.error("导出技术检测报告失败", e);
                        return null;
                    }
                }).whenComplete((v, th) -> {
                    if (th != null) {
                        log.error("", th);
                    }
                    if (v != null) {
                        log.info(v);
                        filePathList.add(v);
                    }
                })
                .thenRun(() -> addScanReportToPath(filePathList, dto.getOperationId()))
        ).thenApply(v -> zip(filePathList, poVo.getOperationName(), basePath));
        return future.get();
    }

    @Override
    @SchemaSwitch(ExportWordDto.class)
    public String process(ExportWordDto dto, QueryProjectOperationExportVo poVo, Long modelId) throws IOException {
        InputStream inputStream;
        ModelFile modelFile = modelFileMapper.selectOne(new QueryWrapper<ModelFile>().eq("model_id", modelId));
        if (modelFile == null) {
            ClassPathResource classPathResource = new ClassPathResource("template/transportationSafetyReport.docx");
            inputStream = classPathResource.getInputStream();
            log.warn("找不到模型ID={}对应的文件,使用默认交通模板", modelId);
        }
        inputStream = Files.newInputStream(Paths.get(modelFile.getFilePath()));

        /*ClassPathResource classPathResource = new ClassPathResource("template/transportationSafetyReport.docx");
        InputStream inputStream = classPathResource.getInputStream();*/

        //数据模型
        Map<String, Object> model = new HashMap<>(16);
        //准备数据
        String operationId = dto.getOperationId();
        List<ExportWordChart> chartList = dto.getChartList();
        List<ExportWordChart> analysisChartList =
                chartList.stream().filter(s -> s.getName().contains("图") || s.getName().contains("表格"))
                        .collect(Collectors.toList());
        //评估内容
        List<Long> serviceContentList = JSON.parseArray("[" + poVo.getServiceContent() + "]", Long.class);

        // 报告公共部分
        putCommonInfo(poVo, model);

        // 各阶段赋值
        putFinishTime(model, operationId);

        // 解析上传的信息调研清单
        parseInformationResearchFile(operationId,model);

        // 3.6 安全措施情况
        safetyMeasureInfo(model, operationId, analysisChartList);

        model.put("lawAssessment", false);
        // 4.数据安全合规评估
        if (serviceContentList.contains(LabelEnum.HFHG.getCode())) {
            model.put("lawAssessment", true);
            lawAssessment(model, operationId);
        }

        model.put("riskAssessment", false);
        // 5.数据安全风险评估
        if (serviceContentList.contains(LabelEnum.SMZQ.getCode())) {
            model.put("riskAssessment", true);
            // 5.1 风险评估内容
            riskAssessment(model, operationId);
            // 5.2.1 识别数据安全风险
            riskIdentification(model, operationId);
            // 5.2.2 风险分析危害程度
            Map<String, String> systemRiskHarmDegree = riskAnalysis(model, operationId);
            // 5.2.3 评价综合影响
            riskInfluence(model, operationId, systemRiskHarmDegree);
            // 5.2.4 量化风险评分
            riskScore(model, operationId);
        }

        model.put("processSuggestions", false);
        if (serviceContentList.contains(LabelEnum.CZJY.getCode())) {
            model.put("processSuggestions", true);
            processSuggestions(operationId, model);
        }

        //配置模板
        LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
        TOCRenderPolicy tocPolicy = new TOCRenderPolicy();
        Configure config =
                Configure.builder().bind("busSystemBaseInfoList", policy).bind("dataCollectInfoList", policy)
                        .bind("dataStorageInfoList", policy).bind("dataTransportInfoList", policy)
                        .bind("dataUseInfoList", policy).bind("dataProviderInfoList", policy)
                        .bind("dataDisclosureInfoList", policy).bind("dataDeleteInfoList", policy)
                        .bind("dataSafetyList", policy).bind("normsList", policy)
                        .bind("technologyList", policy).bind("netList", policy)
                        .bind("personalInfoAssetList", policy).bind("legalList", policy)
                        .bind("dataProcessingList", policy).bind("dataSecurityList", policy)
                        .bind("securityTechList", policy).bind("personalInfoList", policy)
                        .bind("riskResultList", policy).bind("riskInfluenceList", policy)
                        .bind("riskIdentificationList", policy).bind("busSystemSortList", policy)
                        .bind("dataTechSuggests", policy).bind("dataSafetySuggests", policy)
                        .bind("tabList", policy).bind("tocContents", tocPolicy).build();

        //加载模板渲染数据
        XWPFTemplate template = XWPFTemplate.compile(inputStream, config).render(model);
        NiceXWPFDocument xwpfDocument = template.getXWPFDocument();
        xwpfDocument.enforceUpdateFields();

        String realFileName = String.format("%s报告.docx", poVo.getOperationName());
        String path = String.join(File.separator, basePath, "temp", realFileName);
        FileUtil.touch(path);

        //输出结果
        OutputStream out = new FileOutputStream(path);
        BufferedOutputStream bos = new BufferedOutputStream(out);
        xwpfDocument.write(bos);
        bos.flush();
        out.flush();
        PoitlIOUtils.closeQuietlyMulti(template, xwpfDocument, bos, out);
        return path;
    }

    private void safetyMeasureInfo(Map<String, Object> model, String operationId, List<ExportWordChart> analysisChartList) {
        //safetyManagerInfo(model, operationId);

        currentSituationAnalysis(operationId, model, analysisChartList);
    }

    private void riskIdentification(Map<String, Object> model, String operationId) {
        List<RiskIdentificationDTO> riskIdentificationDTOS = adviseRiskMapper.selectRiskIdentification(operationId);
        AtomicInteger sort = new AtomicInteger(1);
        List<Map<String, Object>> riskIdentificationList = new ArrayList<>();
        for (RiskIdentificationDTO riskIdentificationDTO : riskIdentificationDTOS) {
            Map<String, Object> map = new HashMap<>();
            map.put("sort", sort.getAndIncrement());
            map.put("title", riskIdentificationDTO.getTitle());
            map.put("content", riskIdentificationDTO.getContent());
            map.put("riskType", riskIdentificationDTO.getRiskType());
            map.put("riskLevel", riskIdentificationDTO.getRiskLevel());
            riskIdentificationList.add(map);
        }
        model.put("riskIdentificationList", riskIdentificationList);
    }

    @SchemaSwitch(String.class)
    private void riskScore(Map<String, Object> model, String operationId) {
        RiskAnalysisReportVO vo = iRiskAnalysisService.queryRiskAnalysisReport(operationId, true);

        // 综合风险值
        RiskAnalysisReportVO.ComprehensiveChart comprehensiveChart = vo.getComprehensiveChart();
        model.put("totalRiskValue", comprehensiveChart.getValue());
        double totalRiskValue = Double.parseDouble(comprehensiveChart.getValue());
        comprehensiveChart.getDataScopeList().forEach(scope -> {
            if (scope.getStartScope() <= totalRiskValue && scope.getEndScope() >= totalRiskValue){
                model.put("totalRiskLevel", scope.getDisplayName());
            }
        });

        // 业务系统风险排序
        RiskAnalysisReportVO.BusSystemRiskSortChart busSystemRiskSortChart = vo.getBusSystemRiskSortChart();
        List<Map<String, Object>> busSystemSortList = new ArrayList<>();
        AtomicInteger sort = new AtomicInteger(1);
        busSystemRiskSortChart.getXAxisList().forEach(reportConfigBaseDTO -> {
            Map<String, Object> map = new HashMap<>(16);
            map.put("sort", sort.getAndIncrement());
            map.put("bsName", reportConfigBaseDTO.getConfigName());
            map.put("riskValue", reportConfigBaseDTO.getConfigIndicatorValue());
            double riskValue = Double.parseDouble(String.valueOf(reportConfigBaseDTO.getConfigIndicatorValue()));
            comprehensiveChart.getDataScopeList().forEach(scope -> {
                if (scope.getStartScope() <= riskValue && scope.getEndScope() >= riskValue){
                    map.put("riskLevel", scope.getDisplayName());
                }
            });
            busSystemSortList.add(map);
        });
        model.put("busSystemSortList", busSystemSortList);
    }

    /**
     * 4.现状分析
     */
    private void currentSituationAnalysis(String operationId, Map<String, Object> model,
                                          List<ExportWordChart> analysisChartList) {
        //查询现状核验表获取标签页
        QueryWrapper<CoVerification> query = new QueryWrapper<>();
        query.eq("operation_id", operationId);
        List<CoVerification> coVerifications = coVerificationMapper.selectList(query);
        if (CollectionUtils.isEmpty(coVerifications)) {
            return;
        }

        // 排序
        CoVerification coVerification1 = coVerifications.get(0);
        model.put("modelName", coVerification1.getModelName());
        model.put("level", StringUtils.isNotNull(coVerification1.getLevel()) ? coVerification1.getLevel().trim() :
                coVerification1.getLevel());

        //4.1.2.1	评估结果
        List<Map<String, Object>> analysisContents = new ArrayList<>();
        List<ExportWordChart> exportWordCharts = coGapAnalysisMapper.queryRemarkByOperationId(operationId);
        Map<String, String> remarkMap = new HashMap<>();
        if (CollUtil.isNotEmpty(exportWordCharts)){
            remarkMap =
                    exportWordCharts.stream().filter(exportWordChart -> StrUtil.isNotEmpty(exportWordChart.getRemark()))
                            .collect(Collectors.toMap(ExportWordChart::getName, ExportWordChart::getRemark, (k1, k2) -> k1));
        }
        for (ExportWordChart chart : analysisChartList) {
            if (chart.getName().startsWith("基础评估分析-")) {
                continue;
            }
            Map<String, Object> content = new HashMap<>();
            content.put("chartName", chart.getName());
            List<Map<String, Object>> chartImages = new ArrayList<>();
            Map<String, Object> chartMap = new HashMap<>();

            Map<String, String> imageMap = getPicture(Lists.newArrayList(chart));

            chartMap.put("chartImage", getPictureStream(imageMap));
            chartMap.put("chartImageName", chart.getName());
            chartMap.put("chartImageEvaluate", remarkMap.get(chart.getName()) == null ? "": StrUtil.fillBefore(remarkMap.get(chart.getName()), StrPool.C_TAB, 4));
            chartImages.add(chartMap);
            content.put("chartImages", chartImages);
            analysisContents.add(content);
        }
        model.put("analysisContents", analysisContents);

        List<Map<String, Object>> verification = new ArrayList<>();
        Set<String> tabNames = new HashSet<>();
        List<String> sortList = new ArrayList<>();
        // 根据能力项分组
        coVerifications.stream().collect(Collectors.groupingBy(CoVerification::getGpDimension)).forEach((k, list)->{
            AtomicInteger sort = new AtomicInteger(1);
            Map<String, Object> map = new HashMap<>();
            sortList.add(k);
            String tabName = k;
            if (StrUtil.split(k, StrPool.C_SPACE).size() > 1) {
                tabName = StrUtil.subAfter(k, StrUtil.C_SPACE, true);
            }
            tabNames.add(tabName);
            map.put("tabName", tabName);
            map.put("tabList", coVerifications.stream().filter(v -> Objects.equals(v.getGpDimension(), k))
                    .sorted(Comparator.comparingInt((CoVerification v) -> {
                        try {
                            // 去掉点，转换为整数
                            return Integer.parseInt(v.getBpCode().replace(".", ""));
                        } catch (NumberFormatException e) {
                            // 转换失败，排在最后
                            return Integer.MAX_VALUE;
                        }
                    })).peek(s -> {
                        s.setSort(sort.getAndIncrement());
                        s.setDescription(s.getDesc());
                    })
                    .collect(Collectors.toList()));
            verification.add(map);
        });
        List<String> sorts = sortList.stream().sorted(Comparator.comparing(s -> StrUtil.subBefore(s, StrUtil.C_SPACE, true)))
                .map(s->StrUtil.subAfter(s, StrUtil.C_SPACE, true)).collect(
                        Collectors.toList());
        model.put("tabNames", CollUtil.join(tabNames, "、"));
        model.put("verification", verification.stream().sorted(Comparator.comparing(v-> sorts.indexOf(v.get("tabName")))).collect(
                Collectors.toList()));
    }

    private void riskInfluence(Map<String, Object> model, String operationId, Map<String, String> systemRiskHarmDegree) {
        List<Map<String, Object>> riskInfluenceList = new ArrayList<>();
        // TODO 调整区分系统
        List<RiskRefluenceDTO> verificationList = coVerificationMapper.queryRiskRefluence(operationId);
        // 当前作业下已勾选的核查项id或者不适用的核查项 （用于筛选掉部分符合中符合的核查项）
        Set<String> checkItems = questionnaireContentMapper.selectAvailableItems(operationId).stream()
                .filter(q -> q.getChecked() || q.getInapplicable()).map(QuestionnaireContent::getItemId).collect(Collectors.toSet());
        List<QuestionnaireContent> questionnaireContents = questionnaireContentMapper.selectSystemUnCheckItems(operationId);
        AtomicInteger count = new AtomicInteger(1);
        for (RiskRefluenceDTO riskRefluenceDTO : verificationList) {
            if (checkItems.contains(riskRefluenceDTO.getItemId())){
                continue;
            }
            Map<String, Object> riskInfluence = new HashMap<>();
            riskInfluence.put("sort", count.getAndIncrement());
            riskInfluence.put("standardItem", riskRefluenceDTO.getStandardItem());
            riskInfluence.put("content", riskRefluenceDTO.getContent());
            riskInfluence.put("riskType", riskRefluenceDTO.getRiskType());
            riskInfluence.put("riskDegree", systemRiskHarmDegree.values().stream().distinct().collect(Collectors.joining(StrPool.COMMA)));
            riskInfluenceList.add(riskInfluence);
        }
        model.put("riskInfluenceList", riskInfluenceList);
    }

    private Map<String, String> riskAnalysis(Map<String, Object> model, String operationId) {
        // 顺便收集业务系统对应的最高风险危害程度
        Map<String, RiskHarmDegreeDTO> systemRiskHarmDegree = new HashMap<>();
        Map<Long, String> systemMap = dynamicProcessTreeMapper.selectByOperationIdAndTreeId(operationId, LabelEnum.XTDY.getCode())
                .stream().collect(Collectors.toMap(TreeLabelDTO::getTreeId, TreeLabelDTO::getTreeName));
        // 业务系统对应的最高威胁
        Map<String, String> systemThreatMap = midThreatResultMapper.selectHighestThreatWithSystem(operationId).stream().collect(
                Collectors.toMap(m -> systemMap.get(m.getSystemId()), MidThreatResultDTO::getThreatFrequencyTag));

        //风险评估列表
        List<CoModelAnalysisResult> coModelAnalysisResultList = coModelAnalysisResultMapper
                .selectList(new QueryWrapper<CoModelAnalysisResult>().eq("operation_id", operationId));

        String titlesStr = "";
        List<Map> resultList = new ArrayList<>();
        for (CoModelAnalysisResult coModelAnalysisResult : coModelAnalysisResultList){
            titlesStr = coModelAnalysisResult.getTitles();
            List<Map> mapList = JSONUtil.toList(coModelAnalysisResult.getResult(), Map.class);
            resultList.addAll(mapList);
        }
        // 如果超过200行记录，则截取前200
        if (resultList.size() > 200) {
            resultList = resultList.subList(0, 200);
        }
        List<FormConfigTreeVO> titles = JSONUtil.toList(titlesStr, FormConfigTreeVO.class);
        Map<String, String> indexColumnMap = new HashMap<>(16);
        titles.forEach(formConfigTreeVO -> {
            if (formConfigTreeVO.getDataColumn()){
                String title = formConfigTreeVO.getTitle().trim();
                switch (title) {
                    case "所属业务系统":
                        indexColumnMap.put(formConfigTreeVO.getDataIndex(), "busSystemName");
                        break;
                    case "数据类型":
                        indexColumnMap.put(formConfigTreeVO.getDataIndex(), "dataType");
                        break;
                    case "数据级别":
                        indexColumnMap.put(formConfigTreeVO.getDataIndex(), "dataLevel");
                        break;
                    case "风险类型":
                        indexColumnMap.put(formConfigTreeVO.getDataIndex(), "riskType");
                        break;
                    default:
                        break;
                }
            } else {
                if (CollUtil.isNotEmpty(formConfigTreeVO.getChildren())) {
                    formConfigTreeVO.getChildren().forEach(child -> {
                        switch (child.getTitle()){
                            case "资产名称":
                                indexColumnMap.put(child.getDataIndex(), "assetName");
                                break;
                            case "敏感等级":
                                indexColumnMap.put(child.getDataIndex(), "sensitiveLevel");
                                break;
                            case "风险危害程度":
                                indexColumnMap.put(child.getDataIndex(), "riskHazardLevel");
                                break;
                            case "风险危害程度得分":
                                indexColumnMap.put(child.getDataIndex(), "riskHazardGrade");
                                break;
                            default:
                                break;
                        }

                    });
                } else {
                    if ("所属业务系统".equals(formConfigTreeVO.getTitle())) {
                        indexColumnMap.put(formConfigTreeVO.getDataIndex(), "busSystemName");
                    }
                }
            }
        });
        List<Map<String, Object>> riskResultList = new ArrayList<>();
        AtomicInteger sort = new AtomicInteger(1);
        resultList.forEach(map -> {
            Map<String, Object> resultMap = new HashMap<>(16);
            for (Object o : map.keySet()) {
                String key = (String)o;
                if (indexColumnMap.get(key) == null){
                    continue;
                }
                resultMap.put(indexColumnMap.get(key), map.get(key));
            }
            // 风险隐患严重程度，取当前业务系统威胁分析频率最高
            String busSystemName = MapUtil.getStr(resultMap, "busSystemName");
            resultMap.put("riskHiddenDanger", systemThreatMap.get(busSystemName));
            resultMap.put("sort", sort.getAndIncrement());
            RiskHarmDegreeDTO riskHarmDegreeDTO = systemRiskHarmDegree.get(busSystemName);
            Double riskHazardGrade = MapUtil.getDouble(resultMap, "riskHazardGrade");
            String riskHazardLevel = MapUtil.getStr(resultMap, "riskHazardLevel");
            if (Objects.isNull(riskHarmDegreeDTO)) {
                systemRiskHarmDegree.put(busSystemName, new RiskHarmDegreeDTO(riskHazardLevel, riskHazardGrade));
            } else {
                if (riskHazardGrade > riskHarmDegreeDTO.getScore()) {
                    systemRiskHarmDegree.put(busSystemName, new RiskHarmDegreeDTO(riskHazardLevel, riskHazardGrade));
                }
            }
            riskResultList.add(resultMap);
        });
        model.put("riskResultList", riskResultList);
        return systemRiskHarmDegree.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().getDegree()));
    }

    private void riskAssessment(Map<String, Object> model, String operationId) {
        List<CoVerification> verificationList = coVerificationMapper.selectList(new QueryWrapper<CoVerification>().eq("operation_id", operationId));

        Map<String, List<Map<String, Object>>> stageMap = new HashMap<>();
        stageMap.put("1 数据处理活动", new ArrayList<>());
        stageMap.put("2 数据安全管理", new ArrayList<>());
        stageMap.put("3 数据安全技术", new ArrayList<>());
        stageMap.put("4 个人信息保护", new ArrayList<>());

        // 按stage分组，然后对每组内的process进行排序
        Map<String, List<CoVerification>> groupedByStage = verificationList.stream()
                .filter(item -> item.getStage() != null && item.getProcess() != null)
                .collect(Collectors.groupingBy(
                        CoVerification::getStage,
                        // 使用LinkedHashMap保持插入顺序
                        LinkedHashMap::new,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> {
                                    // 对每个stage组内的数据按process序号排序
                                    list.sort((a, b) -> {
                                        try {
                                            // 提取process前面的序号（如"1.3"中的1.3）
                                            String processA = a.getProcess().trim();
                                            String processB = b.getProcess().trim();

                                            // 提取序号部分
                                            String numA = processA.split("\\s+")[0];
                                            String numB = processB.split("\\s+")[0];

                                            // 转换为Double进行比较（支持1.3这种格式）
                                            Double valueA = Double.parseDouble(numA);
                                            Double valueB = Double.parseDouble(numB);

                                            return valueA.compareTo(valueB);
                                        } catch (Exception e) {
                                            // 如果解析失败，按字符串排序
                                            return a.getProcess().compareTo(b.getProcess());
                                        }
                                    });
                                    return list;
                                }
                        )
                ));

        for (Map.Entry<String, List<CoVerification>> entry : groupedByStage.entrySet()) {
            String stage = entry.getKey();
            List<Map<String, Object>> stageInfo = stageMap.get(stage);
            List<CoVerification> stageList = entry.getValue();
            AtomicInteger processSort = new AtomicInteger(1);
            for (CoVerification v : stageList) {
                Map<String, Object> processMap = new HashMap<>();
                processMap.put("sort", processSort.getAndIncrement());
                processMap.put("subarea", v.getProcess());
                processMap.put("item", v.getStandardProvision());
                String result;
                if (StrUtil.equals(v.getResult(), "完全符合")) {
                    result = "符合";
                } else if (StrUtil.equals(v.getResult(), "不适用")) {
                    result = "不适用";
                } else {
                    result = "不符合";
                }
                processMap.put("result", result);
                String describe = StrUtil.EMPTY;
                if (StrUtil.equals(result, "符合")) {
                    describe = v.getDescription();
                } else if (StrUtil.equals("不符合", result)) {
                    describe = v.getIncompatible();
                }
                processMap.put("describe", describe);
                stageInfo.add(processMap);
            }
        }

        model.put("dataProcessingList", stageMap.get("1 数据处理活动"));
        model.put("dataSecurityList", stageMap.get("2 数据安全管理"));
        model.put("securityTechList", stageMap.get("3 数据安全技术"));
        model.put("personalInfoList", stageMap.get("4 个人信息保护"));
    }

    private void parseInformationResearchFile(String operationId, Map<String, Object> model) {
        CoFile coFile = coFileMapper.queryFileByOperationIdAndFileType(operationId, FileType.RESEARCH.getCode());
        if (coFile == null){
            log.warn("调研文件清单未上传!");
            return;
        }
        List<Sheet> sheets = ExcelUtil.getReader(coFile.getFilePath()).getSheets();
        for (Sheet sheet : sheets){
            switch (sheet.getSheetName()){
                case "【评估单位信息】基本信息":
                    parseSheetOne(sheet, model);
                    break;
                case "【被评估方信息】数据处理者调研":
                    parseSheetTwo(sheet, model);
                    break;
                case "【被评估方信息】业务和信息系统调研":
                    parseSheetThree(sheet, model);
                    break;
                case "【被评估方信息】数据处理活动调研":
                    parseSheetFour(sheet, model);
                    break;
                case "【被评估方信息】个人信息调研":
                    parseSheetFive(sheet, model);
                    break;
                default:
                    break;
            }
        }
    }

    private void parseSheetOne(Sheet sheet, Map<String, Object> model) {
        // sheet1从第二行开始
        for (int i=4; i<=sheet.getLastRowNum(); i++) {
            if (i == 4) {
                // 评估单位名称
                model.put("evaluationUnitName", getCellValue(sheet.getRow(i).getCell(3)));
            } else if (i == 5) {
                // 评估单位地址
                model.put("evaluationUnitAddress", getCellValue(sheet.getRow(i).getCell(3)));
            } else if (i == 6) {
                // 邮政编码
                model.put("evaluationUnitPostalCode", getCellValue(sheet.getRow(i).getCell(3)));
            }
        }
    }

    private void parseSheetTwo(Sheet sheet, Map<String, Object> model) {
        // sheet1从第二行开始
        List<Object> companyBaseInfoList = new ArrayList<>();
        for (int i=3; i<=sheet.getLastRowNum(); i++) {
            if (i == 3) {
                // 数据处理者基本情况
                companyBaseInfoList.add(getCellValue(sheet.getRow(i).getCell(3)));
            } else if (i == 4) {
                // 数据处理者单位名称、组织机构代码、办公地址
                Object cellValue3 = getCellValue(sheet.getRow(i).getCell(3));
                companyBaseInfoList.add(cellValue3);
                model.put("cyName", cellValue3);
                Object cellValue5 = getCellValue(sheet.getRow(i).getCell(5));
                companyBaseInfoList.add(cellValue5);
                model.put("cyCode", cellValue5);
                Object cellValue7 = getCellValue(sheet.getRow(i).getCell(7));
                companyBaseInfoList.add(cellValue7);
                model.put("cyAddr", cellValue7);
            } else if (i == 5) {
                // 数据处理者法人代表、人员规模、经营范围
                companyBaseInfoList.add(getCellValue(sheet.getRow(i).getCell(3)));
                companyBaseInfoList.add(getCellValue(sheet.getRow(i).getCell(5)));
                companyBaseInfoList.add(getCellValue(sheet.getRow(i).getCell(7)));
            } else if (i == 6) {
                // 数据处理者项目联系人、项目联系人联系方式、邮政编码
                Object cellValue3 = getCellValue(sheet.getRow(i).getCell(3));
                companyBaseInfoList.add(cellValue3);
                model.put("proName", cellValue3);
                Object cellValue5 = getCellValue(sheet.getRow(i).getCell(5));
                companyBaseInfoList.add(cellValue5);
                model.put("proContact", cellValue5);
                Object cellValue7 = getCellValue(sheet.getRow(i).getCell(7));
                companyBaseInfoList.add(cellValue7);
                model.put("postCode", cellValue7);
            }  else if (i == 7) {
                // 数据处理者项目联系人所属部门、项目联系人职务、数据安全负责人（*）
                Object cellValue3 = getCellValue(sheet.getRow(i).getCell(3));
                model.put("proDept", cellValue3);
                Object cellValue5 = getCellValue(sheet.getRow(i).getCell(5));
                model.put("proDuty", cellValue5);
                Object cellValue7 = getCellValue(sheet.getRow(i).getCell(7));
                companyBaseInfoList.add(cellValue7);
                model.put("dsOfficer", cellValue7);
            } else if (i == 8){
                // 单位类型（党政机关、事业单位、企业、社会团体、其他）
                model.put("dataProcessorUnitType", getCellValue(sheet.getRow(i).getCell(3)));
            } else if (i == 9){
                // 特定类型数据处理者（如政务数据处理者、大型网络平台运营者、关键信息基础设施运营者等）
                model.put("dataProcessorSpecial", getCellValue(sheet.getRow(i).getCell(3)));
            } else if (i == 10){
                // 所属行业领域
                //（公共通信和信息服务、能源、交通、水利、金融、公共服务、电子政务、国防科技工业、卫生健康、教育、科技、文化、农业等）
                model.put("dataProcessorIndustrySector", getCellValue(sheet.getRow(i).getCell(3)));
            } else if (i == 11){
                // 上级主管部门
                model.put("dataProcessorParentDept", getCellValue(sheet.getRow(i).getCell(3)));
            } else if (i == 12){
                // 业务运营地区
                model.put("dataProcessorZone", getCellValue(sheet.getRow(i).getCell(3)));
            } else if (i == 13){
                // 主要业务范围、业务规模
                model.put("dataProcessorBusinessScope", getCellValue(sheet.getRow(i).getCell(3)));
            } else if (i == 14){
                // 数据处理相关服务取得行政许可的情况
                model.put("dataProcessorLicense", getCellValue(sheet.getRow(i).getCell(3)));
            } else if (i == 15){
                // 资本组成和实际控制人情况
                model.put("dataProcessorControlPerson", getCellValue(sheet.getRow(i).getCell(3)));
            } else if (i == 16){
                // 境外上市或计划赴境外上市及境外资本参与情况
                //（是，上市地区和交易所；计划上市，计划上市地区和交易所；以协议控制（VIE）架构等方式实质性境外上市；否）
                model.put("dataProcessorIPO", getCellValue(sheet.getRow(i).getCell(3)));
            } else if (i == 17) {
                // 处理数据级别 （一般数据；重要数据；核心数据；自定义分级；未分级）
                model.put("dataProcessorLevel", getCellValue(sheet.getRow(i).getCell(3)));
            } else if (i == 18) {
                // 处理数据的规模 （GE级；PB级；EB级；人；条；其他）
                model.put("dataProcessorScale", getCellValue(sheet.getRow(i).getCell(3)));
            } else if (i == 19) {
                // 处理的数据范围 （全国；省级；市级；区县级；其他）
                model.put("dataProcessorScope", getCellValue(sheet.getRow(i).getCell(3)));
            } else if (i == 20) {
                // 主要涉及的数据处理活动 （数据收集、数据存储、数据传输、数据使用和加工、数据提供、数据公开、数据删除）
                model.put("dataProcessorActivity", getCellValue(sheet.getRow(i).getCell(3)));
            }
        }
        // 单位基本信息
        String companyBaseInfo = companyBaseInfoList.stream().filter(Objects::nonNull).map(Object::toString).collect(Collectors.joining(StrPool.LF));
        model.put("companyBaseInfo", companyBaseInfo);
    }

    private void parseSheetThree(Sheet sheet, Map<String, Object> model) {
        for (int i = 3; i <= 5; i++) {
            if (i == 3) {
                // 网络规模
                model.put("busSystemNetworkScale", getCellValue(sheet.getRow(i).getCell(4)));
            } else if (i == 4) {
                // 拓扑结构
                model.put("busSystemNetworkTopology", getCellValue(sheet.getRow(i).getCell(4)));
            } else {
                // 信息系统对外连接情况
                model.put("busSystemConnectInfo", getCellValue(sheet.getRow(i).getCell(4)));
            }
        }

        // 业务系统基本信息
        // 业务系统基本信息
        Map<Integer, Object> busSystemMap = new HashMap<>(16);
        parseBusSystemBaseInfo(sheet, model, busSystemMap);

        // 业务涉及数据情况 第14-18行
//        parseBusSystemDataInfo(sheet, model);

        //信息系统、App 和小程序情况 第20-25行
        parseBusSystemAppInfo(sheet, model, busSystemMap);

        // 接入的外部第三方产品、服务或 SDK 的情况
        parseBusSystemProductInfo(sheet, model, busSystemMap);
    }

    private void parseSheetFour(Sheet sheet, Map<String, Object> model) {
        // 数据收集情况
        parseDataCollectInfo(sheet, model);

        // 数据存储情况
        parseDataStorageInfo(sheet, model);

        // 数据传输情况
        parseDataTransportInfo(sheet, model);

        // 数据使用和加工情况
        parseDataUsageInfo(sheet, model);

        // 数据提供情况
        parseDataProviderInfo(sheet, model);

        // 数据公开情况
        parseDataDisclosureInfo(sheet, model);

        // 数据删除情况
        parseDataDeleteInfo(sheet, model);

        // 数据出境情况
        parseDataExportInfo(sheet, model);
    }

    private void parseSheetFive(Sheet sheet, Map<String, Object> model) {
        // 个人信息调研
        parsePersonalInfo(sheet, model);
        // 个人信息基本情况
        parsePersonalBaseDetail(sheet, model);
    }

    private void parsePersonalBaseDetail(Sheet sheet, Map<String, Object> model) {
        for (int i = 8; i <= sheet.getLastRowNum(); i++) {
            if (i == 8) { // 是否处理个人信息
                model.put("isProcessPersonal", getCellValue(sheet.getRow(i).getCell(3)));
            } else if (i == 9) { // 是否运营互联网平台
                model.put("isRunInternetPlatform", getCellValue(sheet.getRow(i).getCell(3)));
            } else if (i == 10) { // 个人信息收集目的
                model.put("personalInfoCollectPurpose", getCellValue(sheet.getRow(i).getCell(3)));
            }// 处理个人信息规模
            else if (i == 11) {
                model.put("personalInfoScale", getCellValue(sheet.getRow(i).getCell(3)));
            }// 处理个人信息范围
            else if (i == 12) {
                model.put("personalInfoScope", getCellValue(sheet.getRow(i).getCell(3)));
            }// 是否利用个人信息进行自动化决策
            else if (i == 13) {
                model.put("isAutoDecision", getCellValue(sheet.getRow(i).getCell(3)));
            }// 是否处理敏感个人信息
            else if (i == 14) {
                model.put("isProcessSensitiveInfo", getCellValue(sheet.getRow(i).getCell(3)));
            }// 处理的敏感个人信息类别
            else if (i == 15) {
                model.put("sensitiveInfoType", getCellValue(sheet.getRow(i).getCell(3)));
            }
        }
    }

    private void parsePersonalInfo(Sheet sheet, Map<String, Object> model) {
        Map<Integer, List<Map<String, Object>>> rowMap = new HashMap<>(16);
        for (int k = 3; k <= 6; k++) {
            if (k == 3) {
                getBusSystem(rowMap, "infoType", sheet.getRow(k), 3);
            } else if (k == 4){
                getBusSystem(rowMap, "infoContent", sheet.getRow(k) ,3);
            } else if (k == 5){
                getBusSystem(rowMap, "infoVolume", sheet.getRow(k), 3);
            } else if (k == 6){
                getBusSystem(rowMap, "infoProcessNode", sheet.getRow(k), 3);
            }
        }
        List<Map<String, Object>> personalInfoAssetList = new ArrayList<>();
        int sort = 1;
        for (Map.Entry<Integer, List<Map<String, Object>>> entry : rowMap.entrySet()){
            Map<String, Object> baseMap = new HashMap<>(16);
            baseMap.put("sort", sort++);
            entry.getValue().forEach(baseMap::putAll);
            personalInfoAssetList.add(baseMap);
        }
        model.put("personalInfoAssetList", personalInfoAssetList);
    }

    private void parseDataExportInfo(Sheet sheet, Map<String, Object> model) {
        Map<Integer, List<Map<String, Object>>> rowMap = new HashMap<>(16);
        getBusSystem(rowMap, "desc", sheet.getRow(51), 4);
        List<Map<String, Object>> dataExportInfoList = new ArrayList<>();
        int sort = 1;
        for (Map.Entry<Integer, List<Map<String, Object>>> entry : rowMap.entrySet()){
            Map<String, Object> dataExportInfoMap = new HashMap<>(16);
            dataExportInfoMap.put("sort", sort++);
            entry.getValue().forEach(dataExportInfoMap::putAll);
            dataExportInfoList.add(dataExportInfoMap);
        }
        String dataExportInfo = dataExportInfoList.stream().map(s->String.valueOf(s.get("desc"))).collect(Collectors.joining(StrPool.LF));
        model.put("dataExportInfo", dataExportInfo);
    }

    private void parseDataDeleteInfo(Sheet sheet, Map<String, Object> model) {
        Map<Integer, List<Map<String, Object>>> rowMap = new HashMap<>(16);
        for (int k = 47; k <= 50; k++) {
            if (k == 47) {
                getBusSystem(rowMap, "purpose", sheet.getRow(k), 4);
            } else if (k == 48){
                getBusSystem(rowMap, "way", sheet.getRow(k), 4);
            } else if (k == 49){
                getBusSystem(rowMap, "achvied", sheet.getRow(k), 4);
            } else{
                getBusSystem(rowMap, "isDel", sheet.getRow(k), 4);
            }
        }
        List<Map<String, Object>> dataDeleteInfoList = new ArrayList<>();
        int sort = 1;
        for (Map.Entry<Integer, List<Map<String, Object>>> entry : rowMap.entrySet()){
            Map<String, Object> dataDeleteInfoMap = new HashMap<>(16);
            dataDeleteInfoMap.put("sort", sort++);
            entry.getValue().forEach(dataDeleteInfoMap::putAll);
            dataDeleteInfoList.add(dataDeleteInfoMap);
        }
        model.put("dataDeleteInfoList", dataDeleteInfoList);
    }

    private void parseDataDisclosureInfo(Sheet sheet, Map<String, Object> model) {
        Map<Integer, List<Map<String, Object>>> rowMap = new HashMap<>(16);
        for (int k = 40; k <= 46; k++) {
            if (k == 40) {
                getBusSystem(rowMap, "purpose", sheet.getRow(k),4);
            } else if (k == 41){
                getBusSystem(rowMap, "way", sheet.getRow(k), 4);
            } else if (k == 42){
                getBusSystem(rowMap, "scope", sheet.getRow(k), 4);
            }  else if (k == 43){
                getBusSystem(rowMap, "amount", sheet.getRow(k), 4);
            } else if (k == 44){
                getBusSystem(rowMap, "industry", sheet.getRow(k), 4);
            } else if (k == 45){
                getBusSystem(rowMap, "organization", sheet.getRow(k), 4);
            } else{
                getBusSystem(rowMap, "region", sheet.getRow(k), 4);
            }
        }
        List<Map<String, Object>> dataDisclosureInfoList = new ArrayList<>();
        int sort = 1;
        for (Map.Entry<Integer, List<Map<String, Object>>> entry : rowMap.entrySet()){
            Map<String, Object> dataDisclosureInfoMap = new HashMap<>(16);
            dataDisclosureInfoMap.put("sort", sort++);
            entry.getValue().forEach(dataDisclosureInfoMap::putAll);
            dataDisclosureInfoList.add(dataDisclosureInfoMap);
        }
        model.put("dataDisclosureInfoList", dataDisclosureInfoList);
    }

    private void parseDataProviderInfo(Sheet sheet, Map<String, Object> model) {
        Map<Integer, List<Map<String, Object>>> rowMap = new HashMap<>(16);
        for (int k = 29; k <= 39; k++) {
            if (k == 29) {
                getBusSystem(rowMap, "dataProviderPurpose", sheet.getRow(k), 4);
            } else if (k == 30){
                getBusSystem(rowMap, "dataProviderWay", sheet.getRow(k), 4);
            } else if (k == 31){
                getBusSystem(rowMap, "dataProviderScope", sheet.getRow(k), 4);
            }  else if (k == 32){
                getBusSystem(rowMap, "dataRecipient", sheet.getRow(k), 4);
            } else if (k == 33){
                getBusSystem(rowMap, "hasProtocol", sheet.getRow(k), 4);
            } else if (k == 34){
                getBusSystem(rowMap, "external", sheet.getRow(k), 4);
            } else if (k == 35){
                getBusSystem(rowMap, "externalType", sheet.getRow(k), 4);
            } else if (k == 36){
                getBusSystem(rowMap, "amount", sheet.getRow(k), 4);
            }  else if (k == 37){
                getBusSystem(rowMap, "scope", sheet.getRow(k),4);
            } else if (k == 38){
                getBusSystem(rowMap, "sensitiveLevel", sheet.getRow(k), 4);
            } else {
                getBusSystem(rowMap, "saveTerm", sheet.getRow(k), 4);
            }
        }
        List<Map<String, Object>> dataProviderInfoList = new ArrayList<>();
        int sort = 1;
        for (Map.Entry<Integer, List<Map<String, Object>>> entry : rowMap.entrySet()){
            Map<String, Object> dataProviderInfoMap = new HashMap<>(16);
            dataProviderInfoMap.put("sort", sort++);
            entry.getValue().forEach(dataProviderInfoMap::putAll);
            dataProviderInfoList.add(dataProviderInfoMap);
        }
        model.put("dataProviderInfoList", dataProviderInfoList);
    }

    private void parseDataUsageInfo(Sheet sheet, Map<String, Object> model) {
        Map<Integer, List<Map<String, Object>>> rowMap = new HashMap<>(16);
        for (int k = 20; k <= 28; k++) {
            if (k == 20) {
                getBusSystem(rowMap, "dataUsagePurpose", sheet.getRow(k), 4);
            } else if (k == 21){
                getBusSystem(rowMap, "dataUsageWay", sheet.getRow(k), 4);
            } else if (k == 22){
                getBusSystem(rowMap, "dataUsageScope", sheet.getRow(k), 4);
            }  else if (k == 23){
                getBusSystem(rowMap, "dataUsageScene", sheet.getRow(k), 4);
            } else if (k == 24){
                getBusSystem(rowMap, "dataUsageRule", sheet.getRow(k), 4);
            } else if (k == 25){
                getBusSystem(rowMap, "dataUsageDept", sheet.getRow(k), 4);
            } else if (k == 26){
                getBusSystem(rowMap, "dataUsageOther", sheet.getRow(k),4);
            } else if (k == 27){
                getBusSystem(rowMap, "dataUsageNetWork", sheet.getRow(k), 4);
            } else {
                getBusSystem(rowMap, "dataUsageEntrust", sheet.getRow(k), 4);
            }
        }
        List<Map<String, Object>> dataUseInfoList = new ArrayList<>();
        int sort = 1;
        for (Map.Entry<Integer, List<Map<String, Object>>> entry : rowMap.entrySet()){
            Map<String, Object> dataUseInfoMap = new HashMap<>(16);
            dataUseInfoMap.put("sort", sort++);
            entry.getValue().forEach(dataUseInfoMap::putAll);
            dataUseInfoList.add(dataUseInfoMap);
        }
        model.put("dataUseInfoList", dataUseInfoList);
    }

    private void parseDataTransportInfo(Sheet sheet, Map<String, Object> model) {
        Map<Integer, List<Map<String, Object>>> rowMap = new HashMap<>(16);
        for (int k = 16; k <= 19; k++) {
            if (k == 16) {
                getBusSystem(rowMap, "way", sheet.getRow(k), 4);
            } else if (k == 17){
                getBusSystem(rowMap, "protocol", sheet.getRow(k), 4);
            } else if (k == 18){
                getBusSystem(rowMap, "dataShare", sheet.getRow(k), 4);
            } else {
                getBusSystem(rowMap, "dataApi", sheet.getRow(k), 4);
            }
        }
        List<Map<String, Object>> dataTransportInfoList = new ArrayList<>();
        int sort = 1;
        for (Map.Entry<Integer, List<Map<String, Object>>> entry : rowMap.entrySet()){
            Map<String, Object> dataTransportInfoMap = new HashMap<>(16);
            dataTransportInfoMap.put("sort", sort++);
            entry.getValue().forEach(dataTransportInfoMap::putAll);
            dataTransportInfoList.add(dataTransportInfoMap);
        }
        model.put("dataTransportInfoList", dataTransportInfoList);
    }

    private void parseDataStorageInfo(Sheet sheet, Map<String, Object> model) {
        Map<Integer, List<Map<String, Object>>> rowMap = new HashMap<>(16);
        for (int k = 12; k <= 15; k++) {
            if (k == 12) {
                getBusSystem(rowMap, "name", sheet.getRow(k), 4);
            } else if (k == 13){
                getBusSystem(rowMap, "address", sheet.getRow(k), 4);
            } else if (k == 14){
                getBusSystem(rowMap, "term", sheet.getRow(k), 4);
            } else {
                getBusSystem(rowMap, "strategy", sheet.getRow(k), 4);
            }
        }
        List<Map<String, Object>> dataStorageInfoList = new ArrayList<>();
        int sort = 1;
        for (Map.Entry<Integer, List<Map<String, Object>>> entry : rowMap.entrySet()){
            Map<String, Object> dataStorageInfoMap = new HashMap<>(16);
            dataStorageInfoMap.put("sort", sort++);
            entry.getValue().forEach(dataStorageInfoMap::putAll);
            dataStorageInfoList.add(dataStorageInfoMap);
        }
        model.put("dataStorageInfoList", dataStorageInfoList);
    }

    private void parseDataCollectInfo(Sheet sheet, Map<String, Object> model) {
        Map<Integer, List<Map<String, Object>>> rowMap = new HashMap<>(16);
        for (int k = 3; k <= 11; k++) {
            if (k == 3) {
                getBusSystem(rowMap, "channel", sheet.getRow(k), 4);
            } else if (k == 4){
                getBusSystem(rowMap, "way", sheet.getRow(k), 4);
            } else if (k == 5){
                getBusSystem(rowMap, "scope", sheet.getRow(k), 4);
            } else if (k == 6){
                getBusSystem(rowMap, "purpose", sheet.getRow(k), 4);
            }  else if (k == 7){
                getBusSystem(rowMap, "frequency", sheet.getRow(k), 4);
            }  else if (k == 8){
                getBusSystem(rowMap, "outerSource", sheet.getRow(k), 4);
            }  else if (k == 9){
                getBusSystem(rowMap, "hasProtocol", sheet.getRow(k), 4);
            }  else if (k == 10){
                getBusSystem(rowMap, "relSystem", sheet.getRow(k), 4);
            } else {
                getBusSystem(rowMap, "outerDevice", sheet.getRow(k), 4);
            }
        }
        List<Map<String, Object>> dataCollectInfoList = new ArrayList<>();
        int sort = 1;
        for (Map.Entry<Integer, List<Map<String, Object>>> entry : rowMap.entrySet()){
            Map<String, Object> dataCollectInfoMap = new HashMap<>(16);
            dataCollectInfoMap.put("sort", sort++);
            entry.getValue().forEach(dataCollectInfoMap::putAll);
            dataCollectInfoList.add(dataCollectInfoMap);
        }
        model.put("dataCollectInfoList", dataCollectInfoList);
    }

    private void parseBusSystemProductInfo(Sheet sheet, Map<String, Object> model, Map<Integer, Object> busSystemMap) {
        Map<Integer, List<Map<String, Object>>> rowMap = new HashMap<>(16);
        for (int k = 27; k <= 31; k++) {
            if (k == 27) {
                getBusSystem(rowMap, "name", sheet.getRow(k), 4);
            } else if (k == 28){
                getBusSystem(rowMap, "release", sheet.getRow(k), 4);
            } else if (k == 29){
                getBusSystem(rowMap, "provider", sheet.getRow(k), 4);
            } else if (k == 30){
                getBusSystem(rowMap, "purpose", sheet.getRow(k), 4);
            } else {
                getBusSystem(rowMap, "sign", sheet.getRow(k), 4);
            }
        }
        List<Map<String, Object>> busSystemProductInfoList = new ArrayList<>();
        int sort = 1;
        for (Map.Entry<Integer, List<Map<String, Object>>> entry : rowMap.entrySet()){
            Map<String, Object> productInfoMap = new HashMap<>(16);
            productInfoMap.put("sort", sort++);
            productInfoMap.put("busSystemName", busSystemMap.get(entry.getKey()));
            entry.getValue().forEach(productInfoMap::putAll);
            busSystemProductInfoList.add(productInfoMap);
        }
        model.put("busSystemProductInfoList", busSystemProductInfoList);
    }

    private void parseBusSystemAppInfo(Sheet sheet, Map<String, Object> model, Map<Integer, Object> busSystemMap) {
        Map<Integer, List<Map<String, Object>>> rowMap = new HashMap<>(16);
        for (int k = 20; k <= 25; k++) {
            if (k == 20) {
                getBusSystem(rowMap, "appFunction", sheet.getRow(k), 4);
            } else if (k == 21){
                getBusSystem(rowMap, "appResult", sheet.getRow(k), 4);
            } else if (k == 22){
                getBusSystem(rowMap, "appUrl", sheet.getRow(k), 4);
            } else if (k == 23){
                getBusSystem(rowMap, "appConnect", sheet.getRow(k), 4);
            } else if (k == 24){
                getBusSystem(rowMap, "appDataApi", sheet.getRow(k), 4);
            } else {
                getBusSystem(rowMap, "appName", sheet.getRow(k), 4);
            }
        }
        List<Map<String, Object>> busSystemAppInfoList = new ArrayList<>();
        int sort = 1;
        for (Map.Entry<Integer, List<Map<String, Object>>> entry : rowMap.entrySet()){
            Map<String, Object> appInfoMap = new HashMap<>(16);
            appInfoMap.put("sort", sort++);
            appInfoMap.put("busSystemName", busSystemMap.get(entry.getKey()));
            entry.getValue().forEach(appInfoMap::putAll);
            busSystemAppInfoList.add(appInfoMap);
        }
        model.put("busSystemAppInfoList", busSystemAppInfoList);
    }

    private void parseBusSystemDataInfo(Sheet sheet, Map<String, Object> model) {
        Map<Integer, List<Map<String, Object>>> rowMap = new HashMap<>(16);
        for (int k = 15; k <= 18; k++) {
            if (k == 15) {
                getBusSystem(rowMap, "busSystemName", sheet.getRow(k), 4);
            } else if (k == 16){
                getBusSystem(rowMap, "busSystemDesc", sheet.getRow(k), 4);
            } else if (k == 17){
                getBusSystem(rowMap, "busSystemType", sheet.getRow(k), 4);
            }  else {
                getBusSystem(rowMap, "busSystemRelDept", sheet.getRow(k), 4);
            }
        }
        List<Map<String, Object>> busSystemDataInfoList = new ArrayList<>();
        int sort = 1;
        for (Map.Entry<Integer, List<Map<String, Object>>> entry : rowMap.entrySet()){
            Map<String, Object> busSystemMap = new HashMap<>(16);
            busSystemMap.put("sort", sort++);
            entry.getValue().forEach(busSystemMap::putAll);
            busSystemDataInfoList.add(busSystemMap);
        }
        model.put("busSystemDataInfoList", busSystemDataInfoList);
    }

    private void parseBusSystemBaseInfo(Sheet sheet, Map<String, Object> model, Map<Integer, Object> busSystemMap) {
        Map<Integer, List<Map<String, Object>>> rowMap = new HashMap<>(16);
        for (int k = 7; k <= 33; k++) {
            if (k == 7) {
                getBusSystem(rowMap, "busSystemName", sheet.getRow(k), 4);
            } else if (k == 8){
                getBusSystem(rowMap, "busSystemDesc", sheet.getRow(k), 4);
            } else if (k == 9){
                getBusSystem(rowMap, "busSystemType", sheet.getRow(k), 4);
            } else if (k == 10){
                getBusSystem(rowMap, "busSystemObject", sheet.getRow(k), 4);
            } else if (k == 11){
                getBusSystem(rowMap, "busSystemProcess", sheet.getRow(k), 4);
            } else if (k == 12){
                getBusSystem(rowMap, "busSystemUserSize", sheet.getRow(k), 4);
            } else if (k == 13){
                getBusSystem(rowMap, "busSystemRegion", sheet.getRow(k), 4);
            } else if (k == 14){
                getBusSystem(rowMap, "busSystemRelDept", sheet.getRow(k), 4);
            } else if (k == 15){
                getBusSystem(rowMap, "sysForm", sheet.getRow(k), 4);
            } else if (k == 16){
                getBusSystem(rowMap, "sysContent", sheet.getRow(k), 4);
            } else if (k == 17){
                getBusSystem(rowMap, "sysVolume", sheet.getRow(k), 4);
            } else if (k == 18) {
                getInclude(rowMap, "containsImportant", sheet.getRow(k), 4);
            } else if (k == 19) {
                getInclude(rowMap, "containsCore", sheet.getRow(k), 4);
            } else if (k == 20){
                getBusSystem(rowMap, "sysProcessNode", sheet.getRow(k), 4);
            } else if (k == 22){
                getBusSystem(rowMap, "appFunction", sheet.getRow(k), 4);
            }
        }
        List<Map<String, Object>> busSystemBaseInfoList = new ArrayList<>();
        int sort = 1;
        for (Map.Entry<Integer, List<Map<String, Object>>> entry : rowMap.entrySet()){
            Map<String, Object> baseMap = new HashMap<>(16);
            baseMap.put("sort", sort++);
            entry.getValue().forEach(map -> {
                baseMap.putAll(map);
                if (map.containsKey("busSystemName")){
                    busSystemMap.put(entry.getKey(), map.get("busSystemName"));
                }
            });
            busSystemBaseInfoList.add(baseMap);
        }
        model.put("busSystemBaseInfoList", busSystemBaseInfoList);
    }

    private void getInclude(Map<Integer, List<Map<String, Object>>> rowMap, String containsImportant, Row row, int startCell) {
        for (int j = startCell; j <= row.getLastCellNum(); j++) {
            if (row.getCell(j) == null) {
                continue;
            }
            String value = (String)getCellValue(row.getCell(j));
            if (StrUtil.isEmpty(value)){
                continue;
            }
            Map<String, Object> map = new HashMap<>(16);
            // 使用正则尝试提取数字
            String num = ReUtil.get("\\d+", value, 0);
            if (StrUtil.isEmpty(num)){
                continue;
            }
            // 判断数量是否大于0，如果大于0，则包含，需要注意转换异常
            try {
                if (Integer.parseInt(num) > 0){
                    map.put(containsImportant, "是");
                } else {
                    map.put(containsImportant, "否");
                }
            } catch (Exception e){
                map.put(containsImportant, "否");
            }
            if (rowMap.containsKey(j)){
                rowMap.get(j).add(map);
            } else {
                List<Map<String, Object>> rowList = new ArrayList<>();
                rowList.add(map);
                rowMap.put(j, rowList);
            }
        }
    }

    private void getBusSystem(Map<Integer, List<Map<String, Object>>> rowMap, String key, Row row, int startCell) {
        for (int j = startCell; j <= row.getLastCellNum(); j++) {
            if (row.getCell(j) == null) {
                continue;
            }
            String value = (String)getCellValue(row.getCell(j));
            if (StrUtil.isEmpty(value)){
                continue;
            }
            Map<String, Object> map = new HashMap<>(16);
            map.put(key, value);
            if (rowMap.containsKey(j)){
                rowMap.get(j).add(map);
            } else {
                List<Map<String, Object>> rowList = new ArrayList<>();
                rowList.add(map);
                rowMap.put(j, rowList);
            }
        }
    }

    private Object getCellValue(Cell cell){
        if (cell.getCellType() == CellType.NUMERIC){
            Object val = cell.getNumericCellValue();
            if (DateUtil.isCellDateFormatted(cell)) {
                val = DateUtil.getJavaDate((Double) val); // POI Excel 日期格式转换
            } else {
                if ((Double) val % 1 != 0) {
                    val = new BigDecimal(val.toString());
                } else {
                    val = new DecimalFormat("0").format(val);
                }
            }
            return val;
        } else if (cell.getCellType() == CellType.BOOLEAN){
            return cell.getBooleanCellValue();
        } else if (cell.getCellType() == CellType.FORMULA){
            return cell.getCellFormula();
        } else {
            return cell.getStringCellValue();
        }
    }

    private void safetyManagerInfo(Map<String, Object> model, String operationId) {
        List<QuestionnaireContent> list = questionnaireContentMapper.selectCheckedItemsExcludeInapplicable(operationId);
        Map<String, List<QuestionnaireContent>> map = list.stream().collect(Collectors.groupingBy(QuestionnaireContent::getQuestionId));
        // 数据安全管理组织情况 对应题干1  340da314bcf7bf34d7108d154f905686
        List<Map<String, Object>> dataSafetyList = new ArrayList<>();
        AtomicInteger sort = new AtomicInteger(1);
        Set<String> duplicateSet = new HashSet<>();
        List<QuestionnaireContent> list1 = map.get("340da314bcf7bf34d7108d154f905686");
        if (CollUtil.isNotEmpty(list1)) {
            list1.forEach(questionnaireContent -> {
                if (duplicateSet.contains(questionnaireContent.getItemTitle())){
                    return;
                }
                Map<String, Object> dataSafetyMap = new HashMap<>(16);
                dataSafetyMap.put("sort", sort.getAndIncrement());
                dataSafetyMap.put("item", questionnaireContent.getItemTitle());
                duplicateSet.add(questionnaireContent.getItemTitle());
                dataSafetyList.add(dataSafetyMap);
            });
        }
        model.put("dataSafetyList", dataSafetyList);

        // 数据安全制度规范情况 对应题干2、题干3、题干4  （5ec82097d1d85ee09d3e7ebe6f16c408、8d1eea007e3837e906e5827ba4454316、b705981cf9c5ff14eb0be1c813919b4d）
        List<Map<String, Object>> normsList = new ArrayList<>();
        sort.set(1);
        List<QuestionnaireContent> list2 = map.get("5ec82097d1d85ee09d3e7ebe6f16c408");
        if (CollUtil.isNotEmpty(list2)) {
            list2.forEach(questionnaireContent -> {
                if (duplicateSet.contains(questionnaireContent.getItemTitle())) {
                    return;
                }
                Map<String, Object> normsMap = new HashMap<>(16);
                normsMap.put("sort", sort.getAndIncrement());
                normsMap.put("item", questionnaireContent.getItemTitle());
                duplicateSet.add(questionnaireContent.getItemTitle());
                normsList.add(normsMap);
            });
        }
        List<QuestionnaireContent> list3 = map.get("8d1eea007e3837e906e5827ba4454316");
        if (CollUtil.isNotEmpty(list3)) {
            list3.forEach(questionnaireContent -> {
                if (duplicateSet.contains(questionnaireContent.getItemTitle())){
                    return;
                }
                Map<String, Object> normsMap = new HashMap<>(16);
                normsMap.put("sort", sort.getAndIncrement());
                normsMap.put("item", questionnaireContent.getItemTitle());
                duplicateSet.add(questionnaireContent.getItemTitle());
                normsList.add(normsMap);
            });
        }
        List<QuestionnaireContent> list4 = map.get("b705981cf9c5ff14eb0be1c813919b4d");
        if (CollUtil.isNotEmpty(list4)) {
            list4.forEach(questionnaireContent -> {
                if (duplicateSet.contains(questionnaireContent.getItemTitle())){
                    return;
                }
                Map<String, Object> normsMap = new HashMap<>(16);
                normsMap.put("sort", sort.getAndIncrement());
                normsMap.put("item", questionnaireContent.getItemTitle());
                duplicateSet.add(questionnaireContent.getItemTitle());
                normsList.add(normsMap);
            });
        }
        model.put("normsList", normsList);

        // 数据安全技术措施情况 对应题干5  7bbf61d49499e0111eb2e7aa0adf1d41
        List<Map<String, Object>> technologyList = new ArrayList<>();
        sort.set(1);
        List<QuestionnaireContent> list5 = map.get("7bbf61d49499e0111eb2e7aa0adf1d41");
        if (CollUtil.isNotEmpty(list5)) {
            list5.forEach(questionnaireContent -> {
                if (duplicateSet.contains(questionnaireContent.getItemTitle())){
                    return;
                }
                Map<String, Object> technologyMap = new HashMap<>(16);
                technologyMap.put("sort", sort.getAndIncrement());
                technologyMap.put("item", questionnaireContent.getItemTitle());
                duplicateSet.add(questionnaireContent.getItemTitle());
                technologyList.add(technologyMap);
            });
        }
        model.put("technologyList", technologyList);

        // 网络安全管理和技术措施情况 对应题干6  8c8f15a34f096d0724cfdc3c9aadb361
        List<Map<String, Object>> netList = new ArrayList<>();
        sort.set(1);
        List<QuestionnaireContent> list6 = map.get("8c8f15a34f096d0724cfdc3c9aadb361");
        if (CollUtil.isNotEmpty(list6)) {
            list6.forEach(questionnaireContent -> {
                if (duplicateSet.contains(questionnaireContent.getItemTitle())){
                    return;
                }
                Map<String, Object> netMap = new HashMap<>(16);
                netMap.put("sort", sort.getAndIncrement());
                netMap.put("item", questionnaireContent.getItemTitle());
                duplicateSet.add(questionnaireContent.getItemTitle());
                netList.add(netMap);
            });
        }
        model.put("netList", netList);
    }

    private void lawAssessment(Map<String, Object> model, String operationId) {
        // 4.2.2 合规分析详情
        QueryViewLegalResultVo legalResultVo = queryLegalProportion(operationId);
        model.put("lawTotalNum", legalResultVo.getLawTotalNum());
        model.put("lawDocNum", legalResultVo.getLawDocNum());
        model.put("ruleDocNum", legalResultVo.getRuleDocNum());
        model.put("standardDocNum", legalResultVo.getStandardDocNum());
        model.put("itemTotalNum", legalResultVo.getItemTotalNum());
        model.put("qualifiedProportion", legalResultVo.getQualifiedProportion());
        model.put("countANum", legalResultVo.getCountANum());
        model.put("countBNum", legalResultVo.getCountBNum());
        model.put("countCNum", legalResultVo.getCountCNum());
        model.put("countDNum", legalResultVo.getCountDNum());
        model.put("countAProportion", legalResultVo.getCountAProportion());
        model.put("countBProportion", legalResultVo.getCountBProportion());
        model.put("countCProportion", legalResultVo.getCountCProportion());
        model.put("countDProportion", legalResultVo.getCountDProportion());

        // 4.3评估详情
        List<Map<String, Object>> lawEvaluation = Lists.newArrayList();
        QueryWrapper<CoLegal> queryWrapper2 = new QueryWrapper<>();
        queryWrapper2.eq("operation_id", operationId);
        List<CoLegal> coLegalList = coLegalMapper.selectList(queryWrapper2);
        Map<String, List<LawRiskContentDTO>> lawRiskContentMap =  lawItemMapper.getLawRiskContentByOperationId(operationId).stream().collect(
                Collectors.groupingBy(LawRiskContentDTO::getItemId));

        if (CollUtil.isNotEmpty(coLegalList)) {
            Set<String> lawType = coLegalList.stream().map(CoLegal::getLawName).collect(Collectors.toSet());
            for (String law : lawType) {
                AtomicInteger index = new AtomicInteger(1);
                Map<String, Object> map = new HashMap<>(lawType.size());
                map.put("lawName", law);
                List<LawReportDTO> list = coLegalList.stream()
                        .filter(l -> Objects.equals(l.getLawName(), law) && !OptEnum.D.getInfo().equals(l.getResult()))
                        .map(legal -> LawReportDTO.builder().itemNum(StrUtil.sub(legal.getItemContent(), 0, 10))
                                .itemContent(legal.getItemContent()).itemExplain(legal.getItemExplain())
                                .result(legal.getResult()).remark(legal.getDesc()).content(getRiskContent(lawRiskContentMap.get(legal.getItemId()))).build())
                        .sorted(Comparator.comparing(i -> {
                            int sort = 9999;
                            try {
                                sort = NumberChineseFormatter.chineseToNumber(
                                        StrUtil.subBetween(i.getItemNum(), "第", "条"));
                            } catch (Exception e) {
                                if (StrUtil.containsAny(i.getItemNum(), StrUtil.DOT)) {
                                    try {
                                        sort = Integer.parseInt(StrUtil.subBefore(i.getItemNum(), StrUtil.DOT, false));
                                    } catch (Exception ignore) {
                                    }
                                }
                            }
                            return sort;
                        })).peek(dt -> dt.setSort(index.getAndIncrement())).collect(Collectors.toList());
                map.put("legalList", list);
                lawEvaluation.add(map);
            }
        }
        model.put("lawEvaluation", lawEvaluation);
    }

    private String getRiskContent(List<LawRiskContentDTO> lawRiskContentList) {
        if (CollUtil.isEmpty(lawRiskContentList)){
            return "";
        }
        return lawRiskContentList.stream().map(LawRiskContentDTO::getContent).filter(Objects::nonNull).distinct().collect(Collectors.joining(StrPool.LF));
    }

    private QueryViewLegalResultVo queryLegalProportion(String operationId) {
        QueryLegalDTO retrieveLegal = new QueryLegalDTO();
        retrieveLegal.setOperationId(operationId);
        retrieveLegal.setLabelId(LabelEnum.HFHG.getCode());
        retrieveLegal.setLawName(LegalModelEnum.ZHMB.getInfo());
        return this.getLegalResultList(retrieveLegal);
    }

    private void putFinishTime(Map<String, Object> model, String operationId) {
        List<CoProgress> list = coProgressMapper.selectList(new QueryWrapper<CoProgress>().eq("operation_id", operationId));
        list.forEach(coProgress -> {
            if (LabelEnum.ZZDY.getCode().equals(coProgress.getLabelId())){
                model.put("dywjDate", cn.hutool.core.date.DateUtil.format(coProgress.getUpdateTime() , "yyyy年MM月dd日"));
            }
            if (LabelEnum.XZHY.getCode().equals(coProgress.getLabelId())){
                model.put("aqxzDate", cn.hutool.core.date.DateUtil.format(coProgress.getUpdateTime() , "yyyy年MM月dd日"));
            }
            if (LabelEnum.CJFX.getCode().equals(coProgress.getLabelId())){
                model.put("cjfxDate", cn.hutool.core.date.DateUtil.format(coProgress.getUpdateTime() , "yyyy年MM月dd日"));
            }
        });
    }
}
