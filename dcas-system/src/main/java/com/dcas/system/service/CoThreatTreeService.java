package com.dcas.system.service;

import com.dcas.common.core.domain.TreeSelect;
import com.dcas.common.model.dto.AddThreatAnalysisDTO;
import com.dcas.common.model.vo.ThreatTreeVO;

import java.util.List;

/**
 * fetch data
 *
 * <AUTHOR>
 * @Date 2022/8/10 16:15
 * @ClassName CoThreatTree
 */
public interface CoThreatTreeService {

    /**
     * 获取分类树数据 新的
     * @param operationId
     * @return
     */
    List<TreeSelect> selectTreeListNew(String operationId);

    List<ThreatTreeVO> selectThreatTemplateTree(AddThreatAnalysisDTO dto);
}
