package com.dcas.system.service;

import com.dcas.common.model.query.PermissionJobQuery;
import com.dcas.common.model.req.IdsReq;
import com.dcas.common.model.req.PermissionDetailQueryReq;
import com.dcas.common.model.req.PermissionJobCreateReq;
import com.dcas.common.model.req.PermissionTestInsertReq;
import com.dcas.common.model.vo.PermissionDetailVO;
import com.dcas.common.model.vo.PermissionJobVO;
import com.dcas.common.model.vo.PermissionTestVO;
import com.dcas.common.utils.PageResult;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2024/11/13 17:39
 * @since 1.0.0
 */
public interface DataPermissionService {
    Integer jobCreate(PermissionJobCreateReq req);

    PageResult<PermissionJobVO> jobQuery(PermissionJobQuery query);

    void jobDelete(IdsReq req);

    void jobUpdate(PermissionJobVO req);

    PageResult<PermissionDetailVO> jobDetails(PermissionDetailQueryReq req);

    void jobInfoDelete(Integer id);

    PermissionTestVO jobCheckTest(Integer jobId);

    void jobTestSave(PermissionTestInsertReq req);

    void jobConfirm(Integer jobId, Integer type);
}
