package com.dcas.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.domain.entity.MkAppJob;
import com.dcas.common.enums.*;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.mapper.MkAppJobMapper;
import com.dcas.common.model.other.MkCompareConfig;
import com.dcas.common.model.other.SourceDatabase;
import com.dcas.common.model.req.AbilityJobListReq;
import com.dcas.common.model.vo.ApiJobQueryVO;
import com.dcas.common.model.vo.DynamicAbilityJobVO;
import com.dcas.common.model.vo.FixedAbilityJobVO;
import com.dcas.common.model.vo.SelectItemVO;
import com.dcas.common.utils.file.FileUploadUtils;
import com.dcas.common.domain.entity.AuthorityRules;
import com.dcas.common.domain.entity.SysConfig;
import com.dcas.common.model.other.LabelValueItem;
import com.dcas.common.model.other.OptionTreeSelect;
import com.dcas.common.model.vo.FileSelectVO;
import com.dcas.common.model.other.OptionSelect;
import com.dcas.common.utils.Func;
import com.dcas.common.metadata.config.*;
import com.dcas.common.mapper.AuthorityRulesMapper;
import com.dcas.system.service.CoConnService;
import com.dcas.system.service.ISysConfigService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.json.JacksonJsonParser;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * fetch data
 *
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CoConnServiceImpl implements CoConnService {

    private final ISysConfigService sysConfigService;
    private final AuthorityRulesMapper authorityRulesMapper;
    private final MkAppJobMapper mkAppJobMapper;

    @Override
    @SchemaSwitch(String.class)
    public List<LabelValueItem> getConfigBasic(String operationId, String type) {
        SysConfig res = sysConfigService.selectConfigById(SysConfigEnum.SOURCE_TYPE.getCode());
        if (Objects.isNull(res))
            throw new ServiceException("配置不存在");

        // 知识库配置的数据源权限转换规则
        Set<Long> customSourceType = authorityRulesMapper.selectAllEnabled().stream().mapToLong(AuthorityRules::getSourceType).boxed().collect(Collectors.toSet());
        if (CollUtil.isEmpty(customSourceType))
            return new ArrayList<>();

        String value = res.getLongValue();
        List<LabelValueItem> basic = JSONUtil.toBean(value, new TypeReference<List<LabelValueItem>>() {
        }, true);

        for (LabelValueItem item : basic) {
            Map<SourceType, List<OptionTreeSelect<Long>>> map = Maps.newEnumMap(SourceType.class);
            DataSourceType.getSchemeType(type).stream().filter(s -> customSourceType.contains(s.getCode())).forEach(
                    v -> map.computeIfAbsent(v.getType(), l -> Lists.newArrayList())
                            .add(new OptionTreeSelect<>(v.getName(), v.getCode())));
            item.setOptions(map.entrySet().stream()
                    .map(v -> new OptionTreeSelect<>(v.getKey().getView(), v.getKey().name(), v.getValue())).distinct()
                    .collect(Collectors.toList()));
        }
        return basic;
    }

    @Override
    public List<SelectItemVO<String, Integer>> getMultiByConfigType(Integer configType) {
        List<SelectItemVO<String, Integer>> res = new ArrayList<>();
        DataSourceType type = DataSourceType.getType(configType);
        switch (type) {
            case MYSQL5:
            case MYSQL:
                res.add(new SelectItemVO<>(DataSourceType.MYSQL5.getName(), DataSourceType.MYSQL5.getCode().intValue()));
                res.add(new SelectItemVO<>(DataSourceType.MYSQL.getName(), DataSourceType.MYSQL.getCode().intValue()));
                break;
            case POSTGRE_SQL:
            case PGSQL_14:
                res.add(new SelectItemVO<>(DataSourceType.POSTGRE_SQL.getName(), DataSourceType.POSTGRE_SQL.getCode().intValue()));
                res.add(new SelectItemVO<>(DataSourceType.PGSQL_14.getName(), DataSourceType.PGSQL_14.getCode().intValue()));
                break;
            default:
                res.add(new SelectItemVO<>(type.getName(), type.getCode().intValue()));
                break;
        }
        return res;
    }

    @Override
    public Map<String, Object> getConfigDetails() {
        SysConfig res = sysConfigService.selectConfigById(SysConfigEnum.SOURCE_CONFIG.getCode());
        String value = res.getLongValue();
        JacksonJsonParser parser = new JacksonJsonParser();
        //此处假设读取配置正确，不再追加异常判断逻辑 版本迭代引入问题可以补充异常处理
        Map<String, Object> ret = parser.parseMap(value);
        Map<Integer, List<FileSelectVO>> map = FileUploadUtils.listConfigFile().stream()
                .map(v -> new FileSelectVO(v.getFilename(), v.getKey(), v.getType().getCode()))
                .collect(Collectors.groupingBy(FileSelectVO::getType));
        List<OptionSelect<String>> krb5 = buildOptionSelect(map.get(ConfigFileType.KRB5.getCode()));
        List<OptionSelect<String>> keytab = buildOptionSelect(map.get(ConfigFileType.KEYTAB.getCode()));
        List<OptionSelect<String>> ssl = buildOptionSelect(map.get(ConfigFileType.JAVA_KEY_STORE.getCode()));
        String key = Func.name(HiveConfig::getChoseType);
        String keyKrb5 = Func.name(HiveConfig::getConf);
        String keyKeytab = Func.name(HiveConfig::getKeytab);
        String certificate = Func.name(SslConfig::getCertificate);
        String keyAuth = Authentication.KERBEROS.name().toLowerCase();
        String sslAuth = Authentication.SSL.name().toLowerCase();

        final String keyOptions = "options";
        for (DataSourceType type : Func.useKerberosSource()) {
            List<Map<String, Object>> configList = (List<Map<String, Object>>) ret.get(type.getCode().toString());
            for (Map<String, Object> config : configList) {
                if (!equalParam(key, config)) {
                    continue;
                }
                Map<String, Object> children = (Map<String, Object>) config.get("children");
                List<Map<String, Object>> kerberos = (List<Map<String, Object>>) children.get(keyAuth);
                if (CollUtil.isNotEmpty(kerberos)) {
                    for (Map<String, Object> kv : kerberos) {
                        String param = MapUtil.getStr(kv, "param");
                        if (keyKrb5.equals(param)) {
                            kv.put(keyOptions, krb5);
                        } else if (keyKeytab.equals(param)) {
                            kv.put(keyOptions, keytab);
                        }
                    }
                    // 暂时不存在kerberos与ssl同时存在
                    break;
                }
                List<Map<String, Object>> sslList = (List<Map<String, Object>>) children.get(sslAuth);
                if (CollUtil.isNotEmpty(sslList)) {
                    for (Map<String, Object> kv : sslList) {
                        if (equalParam(certificate, kv)) {
                            kv.put(keyOptions, ssl);
                            break;
                        }
                    }
                }
                break;
            }
        }
        return ret;
    }

    @Override
    public Map<Long, String> sourceType() {
        return Arrays.stream(DataSourceType.values()).collect(Collectors.toMap(DataSourceType::getCode, DataSourceType::getName));
    }

    private boolean equalParam(String key, Map<String, Object> kv) {
        return key.equals(MapUtil.getStr(kv, "param"));
    }

    private List<OptionSelect<String>> buildOptionSelect(List<FileSelectVO> list) {
        if (CollUtil.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return list.stream().map(v -> new OptionSelect<>(v.getName(), v.getValue())).collect(Collectors.toList());
    }

    @Override
    public List<String> getAbilityJobList(AbilityJobListReq request) {
        // 查询MkAppJob记录
        QueryWrapper<MkAppJob> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", request.getOperationId());
        queryWrapper.eq("type", request.getType());

        // 应用基础过滤条件
        if (Objects.nonNull(request.getStatus())) {
            queryWrapper.eq("status", request.getStatus());
        }
        if (Objects.nonNull(request.getSystemId())) {
            queryWrapper.eq("system_id", request.getSystemId());
        }

        List<MkAppJob> jobs = mkAppJobMapper.selectList(queryWrapper);

        if (CollUtil.isEmpty(jobs)) {
            return new ArrayList<>();
        }

        List<String> result = new ArrayList<>();

        for (MkAppJob job : jobs) {
            try {
                // 应用类型过滤
                boolean isFixed = isFixedAbilityType(job.getType());

                // 判断是固定能力类型还是动态能力类型
                if (isFixed) {
                    // 处理固定能力类型 (AbilityType 1 & 2)
                    FixedAbilityJobVO vo = buildFixedAbilityJobVO(job, request);
                    if (vo != null) { // 如果通过过滤条件
                        result.add(JSONUtil.toJsonStr(vo));
                    }
                } else {
                    // 处理动态能力类型
                    DynamicAbilityJobVO vo = buildDynamicAbilityJobVO(job, request);
                    if (vo != null) { // 如果通过过滤条件
                        result.add(JSONUtil.toJsonStr(vo));
                    }
                }
            } catch (Exception e) {
                // 记录错误但继续处理其他作业
            }
        }

        return result;
    }

    @Override
    public void deleteAbilityJob(Long id) {
        mkAppJobMapper.deleteById(id);
    }

    /**
     * 判断是否为固定能力类型 (AbilityType 1 & 2)
     */
    private boolean isFixedAbilityType(Integer type) {
        return type != null && (type.equals(AbilityType.DESENSITIZATION.getCode()) ||
                               type.equals(AbilityType.ENCRYPT.getCode()));
    }

    /**
     * 构建固定能力类型作业VO
     */
    private FixedAbilityJobVO buildFixedAbilityJobVO(MkAppJob job, AbilityJobListReq request) {
        FixedAbilityJobVO vo = new FixedAbilityJobVO();
        vo.setId(job.getId());
        vo.setPlanId(job.getPlanId());
        vo.setSystemId(job.getSystemId());
        vo.setStatus(job.getStatus());

        // 解析paramIn JSON提取字段
        if (job.getParamIn() != null) {
            try {
                // 将paramIn转换为MkCompareConfig对象
                MkCompareConfig config = JSONUtil.toBean(JSONUtil.toJsonStr(job.getParamIn()), MkCompareConfig.class);

                // 从sourceDatabase中提取sourceIp和sourcePort
                SourceDatabase sourceDb = config.getPlanBasic().getSourceDatabase();
                if (sourceDb != null) {
                    vo.setSourceIp(sourceDb.getHost());
                    vo.setSourcePort(sourceDb.getPort());
                }

                // 从targetDatabase中提取targetIp和targetPort
                SourceDatabase targetDb = config.getPlanBasic().getTargetDatabase();
                if (targetDb != null) {
                    vo.setTargetIp(targetDb.getHost());
                    vo.setTargetPort(targetDb.getPort());
                }
            } catch (Exception e) {
                // 解析失败时设置默认值
                log.error("解析固定能力类型作业失败: {}", e.getMessage());
            }
        }

        // 转换paramOut并设置result和color
        String result = transformResult(job);
        vo.setResult(result);
        vo.setColor(getColorByResult(result, true));

        // 应用过滤条件
        if (!passesFixedAbilityFilters(vo, request)) {
            return null; // 不符合过滤条件
        }

        String desc = StrUtil.EMPTY;
        if (Objects.equals(AbilityType.DESENSITIZATION.getCode(), job.getType())) {
            desc = "脱敏能力";
        } else if (Objects.equals(AbilityType.ENCRYPT.getCode(), job.getType())) {
            desc = "加密能力";
        }
        vo.setResult(result + desc);

        return vo;
    }

    /**
     * 构建动态能力类型作业VO
     */
    private DynamicAbilityJobVO buildDynamicAbilityJobVO(MkAppJob job, AbilityJobListReq request) {
        DynamicAbilityJobVO vo = new DynamicAbilityJobVO();
        vo.setId(job.getId());
        vo.setPlanId(job.getPlanId());
        vo.setProductName(job.getRemark()); // 产品名称从remark字段获取
        vo.setSystemId(job.getSystemId());
        vo.setStatus(job.getStatus());

        // 从paramOut获取结果
        String result = getDynamicResult(job);
        vo.setResult(result);
        vo.setColor(getColorByResult(result, false));

        // 应用过滤条件
        if (!passesDynamicAbilityFilters(vo, request)) {
            return null; // 不符合过滤条件
        }
        vo.setResult(result + job.getRemark());

        return vo;
    }

    /**
     * 转换固定能力类型作业的结果
     */
    private String transformResult(MkAppJob job) {
        if (job.getParamOut() == null) {
            return "不具备";
        }

        try {
            // 解析paramOut
            ApiJobQueryVO queryVO = JSONUtil.toBean(JSONUtil.toJsonStr(job.getParamOut()), ApiJobQueryVO.class);
            if (queryVO.getResult() == null) {
                return "不具备";
            }

            // 获取AbilityType
            AbilityType type = AbilityType.getByCode(job.getType());
            if (type == null) {
                return "不具备";
            }

            // 转换结果
            double result = Double.parseDouble(queryVO.getResult().toString());

            switch (type) {
                case DESENSITIZATION:
                case ENCRYPT:
                    if (result == 0) {
                        return "不具备";
                    } else if (result == 0.5) {
                        return "部分具备";
                    } else {
                        return "具备";
                    }
                default:
                    return "不具备";
            }
        } catch (Exception e) {
            return "不具备";
        }
    }

    /**
     * 获取动态能力类型作业的结果
     */
    private String getDynamicResult(MkAppJob job) {
        if (job.getParamOut() == null) {
            return "不具备";
        }

        try {
            // 解析paramOut
            Map<String, Object> paramOutMap = JSONUtil.parseObj(JSONUtil.toJsonStr(job.getParamOut()));
            Object resultObj = paramOutMap.get("result");

            if (resultObj == null) {
                return "不具备";
            }

            // 转换结果: 0=不具备, 1=具备
            int result = Integer.parseInt(resultObj.toString());
            return result == 1 ? "具备" : "不具备";

        } catch (Exception e) {
            return "不具备";
        }
    }

    /**
     * 根据结果获取颜色代码
     */
    private String getColorByResult(String result, boolean isFixedType) {
        if ("具备".equals(result)) {
            return "#1AD999";
        } else if (isFixedType && "部分具备".equals(result)) {
            return "#FF6537";
        } else {
            return "#FF3D55"; // 不具备
        }
    }

    /**
     * 检查固定能力类型作业是否通过过滤条件
     */
    private boolean passesFixedAbilityFilters(FixedAbilityJobVO vo, AbilityJobListReq request) {
        // 结果过滤
        if (StrUtil.isNotEmpty(request.getResult())) {
            return vo.getResult().equals(request.getResult());
        }

        // 源IP过滤
        if (StrUtil.isNotBlank(request.getSourceIp()) &&
            !StrUtil.contains(vo.getSourceIp(), request.getSourceIp())) {
            return false;
        }

        // 源端口过滤
        if (StrUtil.isNotBlank(request.getSourcePort()) &&
            !StrUtil.contains(vo.getSourcePort(), request.getSourcePort())) {
            return false;
        }

        // 目标IP过滤
        if (StrUtil.isNotBlank(request.getTargetIp()) &&
            !StrUtil.contains(vo.getTargetIp(), request.getTargetIp())) {
            return false;
        }

        // 目标端口过滤
        if (StrUtil.isNotBlank(request.getTargetPort()) &&
            !StrUtil.contains(vo.getTargetPort(), request.getTargetPort())) {
            return false;
        }

        return true;
    }

    /**
     * 检查动态能力类型作业是否通过过滤条件
     */
    private boolean passesDynamicAbilityFilters(DynamicAbilityJobVO vo, AbilityJobListReq request) {
        // 结果过滤
        if (StrUtil.isNotEmpty(request.getResult())) {
            return vo.getResult().equals(request.getResult());
        }

        // 产品名称关键字过滤
        return !StrUtil.isNotBlank(request.getProductName()) ||
                StrUtil.contains(vo.getProductName(), request.getProductName());
    }

}
