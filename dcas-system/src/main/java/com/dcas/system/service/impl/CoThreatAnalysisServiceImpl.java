package com.dcas.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.domain.entity.*;
import com.dcas.common.enums.LabelEnum;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.exception.params.FailParamsException;
import com.dcas.common.mapper.*;
import com.dcas.common.model.dto.*;
import com.dcas.common.utils.FrequencyUtil;
import com.dcas.common.utils.bean.BeanUtils;
import com.dcas.common.utils.params.CheckUtil;
import com.dcas.common.model.vo.CoThreatAnalysisVO;
import com.dcas.common.model.vo.SourceBusinessVO;
import com.dcas.common.model.vo.ThreatTreeVO;
import com.dcas.system.service.CoThreatAnalysisService;
import com.dcas.system.service.CoThreatTreeService;
import com.dcas.system.service.IMidThreatResultService;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 威胁分析实现类
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class CoThreatAnalysisServiceImpl extends ServiceImpl<CoThreatAnalysisMapper, CoThreatAnalysis> implements CoThreatAnalysisService {

    private final ThreatTreeMapper threatTreeMapper;
    private final CoThreatAnalysisMapper coThreatAnalysisMapper;
    private final DynamicProcessTreeMapper dynamicProcessTreeMapper;
    private final IndicatorResultMapper indicatorResultMapper;
    private final ThreatTemplateMapper threatTemplateMapper;
    private final ThreatFrequencyMapper threatFrequencyMapper;
    private final ThreatEventMapper threatEventMapper;
    private final IMidThreatResultService iMidThreatResultService;
    private final CoInventoryMapper coInventoryMapper;

    /**
     * 威胁分析--查询业务系统
     *
     * @param operationId
     * @return * @return String
     * @Date 2022/8/10 14:06
     */
    @Override
    public List<SourceBusinessVO.SourceBusiness> retrieveSystem(String operationId) {
        List<TreeLabelDTO> treeLabelDTOS = dynamicProcessTreeMapper.selectByOperationIdAndTreeId(operationId, LabelEnum.XTDY.getCode());
        return treeLabelDTOS.stream().map(treeLabelDTO -> {
            SourceBusinessVO.SourceBusiness sourceBusiness = new SourceBusinessVO.SourceBusiness();
            sourceBusiness.setSystemId(treeLabelDTO.getTreeId());
            sourceBusiness.setBusinessName(treeLabelDTO.getTreeName());
            return sourceBusiness;
        }).collect(Collectors.toList());
    }


    /**
     * 威胁分析--添加
     *
     * @param dto request
     * @return * @return RestResponse
     * <AUTHOR>
     * @Date 2022/5/16 14:51
     */
    @Override
    public int add(AddThreatAnalysisDTO dto) {
        /**
         * 业务逻辑：
         * 添加威胁分析，根据选择的威胁类型计算威胁数量，按二级算，只要有三级被选中，二级数量计为1
         */
        //入参校验
        CheckUtil.checkParams(dto);
        dto.getBusSystem().forEach(busSystem -> {
            CoThreatAnalysis coThreatAnalysis = new CoThreatAnalysis();
            BeanUtils.copyProperties(dto, coThreatAnalysis);
            int threatNum = getThreatNum(dto);
            coThreatAnalysis.setThreatNum(threatNum);
            coThreatAnalysis.setBusSystem(busSystem);
            QueryWrapper<CoThreatAnalysis> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("operation_id", dto.getOperationId());
            queryWrapper.eq("bus_system", busSystem);
            List<CoThreatAnalysis> coThreatAnalyses = coThreatAnalysisMapper.selectList(queryWrapper);
            if (!coThreatAnalyses.isEmpty()) {
                UpdateWrapper<CoThreatAnalysis> updateWrapper = new UpdateWrapper<>();

                updateWrapper.set("threat_num", threatNum);
                updateWrapper.set("threat_type", dto.getThreatType());
                updateWrapper.set("threat_frequency", dto.getThreatFrequency());
                updateWrapper.eq("bus_system", busSystem);
                coThreatAnalysisMapper.update(new CoThreatAnalysis(), updateWrapper);
                return;
            }
            coThreatAnalysisMapper.insert(coThreatAnalysis);
        });
        return 1;
    }

    private int getThreatNum(AddThreatAnalysisDTO dto) {
        List<Long> treeIds = StrUtil.split(dto.getThreatType(), StrUtil.COMMA)
                .stream().mapToLong(Long::parseLong).boxed().collect(Collectors.toList());
        List<ThreatTree> coThreatTrees = threatTreeMapper.selectBatchIds(treeIds);
        // 取大类数量
        return coThreatTrees.stream().map(co -> StrUtil.subBefore(co.getTreeCode(), StrUtil.DASHED, true))
                .collect(Collectors.toSet()).size();
    }

    /**
     * 威胁分析--修改
     *
     * @param dto request
     * @return * @return RestResponse
     * <AUTHOR>
     * @Date 2022/5/16 14:51
     */
    @Override
    public int edit(AddThreatAnalysisDTO dto) {
        /**
         * 业务逻辑：
         * 根据业务系统名称更新威胁分析表
         */

        //入参校验
        CheckUtil.checkParams(dto);

        dto.getBusSystem().forEach(busSystem -> {
            int threatNum = getThreatNum(dto);

            UpdateWrapper<CoThreatAnalysis> updateWrapper = new UpdateWrapper<>();

            updateWrapper.set("threat_num", threatNum);
            updateWrapper.set("threat_type", dto.getThreatType());
            updateWrapper.set("threat_frequency", dto.getThreatFrequency());
            updateWrapper.eq("bus_system", busSystem);
            coThreatAnalysisMapper.update(new CoThreatAnalysis(), updateWrapper);
        });
        return 1;
    }

    /**
     * 查询
     *
     * @param dto request
     * @return * @return List<CoThreatAnalysis>
     * @Date 2022/8/9 14:30
     */
    @SchemaSwitch(CommonDto.class)
    @Override
    public PageInfo<CoThreatAnalysisVO> retrieve(CommonDto dto, Integer pageNo, Integer pageSize) {
        //入参校验
        CheckUtil.checkParams(dto);

        //浅拷贝
        CommonDto commonDTO = new CommonDto();
        BeanUtils.copyProperties(dto, commonDTO);

        QueryWrapper<CoThreatAnalysis> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("operation_id", commonDTO.getOperationId());
        queryWrapper.eq("label_id", commonDTO.getLabelId());
        //分页参数
        PageMethod.startPage(pageNo, pageSize);
        //查询结果
        List<CoThreatAnalysis> coThreatAnalyses = coThreatAnalysisMapper.selectList(queryWrapper);
        // 转换VO
        List<CoThreatAnalysisVO> result = new ArrayList<>();
        // 查询威胁模板id
        Long threatTemplateId = threatTreeMapper.selectThreatLibraryByOperationId(commonDTO.getOperationId());
        List<ThreatTree> allThreatTreeList =
            threatTreeMapper.selectList(new QueryWrapper<ThreatTree>().eq("threat_template_id", threatTemplateId));
        // 查询威胁频率
        List<MidThreatResult> midThreatResults = iMidThreatResultService.list(
            new QueryWrapper<MidThreatResult>().eq("operation_id", commonDTO.getOperationId()));
        Map<String, MidThreatResult> threatResultMap = new HashMap<>(16);
        if (CollUtil.isNotEmpty(midThreatResults)){
            threatResultMap = midThreatResults.stream().collect(Collectors.toMap(MidThreatResult::getKey, Function.identity()));
        }
        List<TreeLabelDTO> treeLabelDTOS = dynamicProcessTreeMapper.selectByOperationIdAndTreeId(commonDTO.getOperationId(), LabelEnum.XTDY.getCode());
        Map<String, Long> buySystemMap = treeLabelDTOS.stream().collect(Collectors.toMap(TreeLabelDTO::getTreeName, TreeLabelDTO::getTreeId));
        Map<String, MidThreatResult> finalThreatResultMap = threatResultMap;
        coThreatAnalyses.forEach(coThreatAnalysis -> {
            CoThreatAnalysisVO vo = new CoThreatAnalysisVO();
            BeanUtils.copyProperties(coThreatAnalysis, vo);
            Long systemId = buySystemMap.get(coThreatAnalysis.getBusSystem());
            vo.setThreatTreeList(buildThreadTreeList(coThreatAnalysis.getThreatType(), allThreatTreeList,
                finalThreatResultMap, systemId));
            result.add(vo);
        });
        //设置分页条件，根据查询结果分页
        return new PageInfo<>(result);
    }

    private List<ThreatTreeVO> buildThreadTreeList(String threatType, List<ThreatTree> allThreatTreeList, Map<String, MidThreatResult> threatResultMap, Long systemId) {
        List<Long> treeIds = JSON.parseArray(StrUtil.concat(true, "[", threatType, "]"), Long.class);
        // 获取威胁分类
        Map<Long,ThreatTree> parentThreatTreeMap =
            allThreatTreeList.stream().filter(threatTree -> threatTree.getParentId() == 0L)
                .collect(Collectors.toMap(ThreatTree::getTreeId, Function.identity()));
        // 威胁分类子类分组
        Map<Long,List<ThreatTree>> selectedThreatTreeMap =
            allThreatTreeList.stream().filter(threatTree -> treeIds.contains(threatTree.getTreeId()))
                .collect(Collectors.groupingBy(ThreatTree::getParentId));
        List<ThreatTreeVO> list = new ArrayList<>();
        if (selectedThreatTreeMap.isEmpty()){
            return list;
        }
        Map<Long, List<ThreatTreeVO>> parentMap = new HashMap<>(16);
        selectedThreatTreeMap.forEach((k,v)->{
            // 威胁分类存在 则直接添加威胁子类
            if (parentMap.containsKey(k)){
                ThreatTree parent = parentThreatTreeMap.get(k);
                selectedThreatTreeMap.get(parent.getTreeId()).forEach(child -> {
                    ThreatTreeVO childVO = new ThreatTreeVO();
                    BeanUtils.copyProperties(child, childVO);
                    String key = systemId +"_"+ child.getTreeCode();
                    MidThreatResult midThreatResult = threatResultMap.get(key);
                    if (midThreatResult != null) {
                        childVO.setThreatFrequencyTag(midThreatResult.getThreatFrequencyTag());
                        childVO.setThreatFrequencyLevel(midThreatResult.getThreatFrequencyLevel());
                    }
                    parentMap.get(k).add(childVO);
                });
            } else {
                // 威胁分类不存在则新添加威胁分类威胁子类
                if (parentThreatTreeMap.containsKey(k)) {
                    ThreatTree parent = parentThreatTreeMap.get(k);
                    ThreatTreeVO vo = new ThreatTreeVO();
                    BeanUtils.copyProperties(parent, vo);
                    String key = systemId +"_"+ parent.getTreeCode();
                    MidThreatResult parentMidThreatResult = threatResultMap.get(key);
                    if (parentMidThreatResult != null) {
                        vo.setThreatFrequencyTag(parentMidThreatResult.getThreatFrequencyTag());
                        vo.setThreatFrequencyLevel(parentMidThreatResult.getThreatFrequencyLevel());
                    }
                    List<ThreatTreeVO> childList = new ArrayList<>();
                    selectedThreatTreeMap.get(parent.getTreeId()).forEach(child -> {
                        ThreatTreeVO childVO = new ThreatTreeVO();
                        BeanUtils.copyProperties(child, childVO);
                        String keyId = systemId +"_"+ child.getTreeCode();
                        MidThreatResult midThreatResult = threatResultMap.get(keyId);
                        if (midThreatResult != null) {
                            childVO.setThreatFrequencyTag(midThreatResult.getThreatFrequencyTag());
                            childVO.setThreatFrequencyLevel(midThreatResult.getThreatFrequencyLevel());
                        }
                        childList.add(childVO);
                    });
                    vo.setChildList(childList);
                    list.add(vo);
                    parentMap.put(k, list);
                } else if(k==0){
                    selectedThreatTreeMap.get(0L).forEach(child->{
                        ThreatTreeVO childVO = new ThreatTreeVO();
                        BeanUtils.copyProperties(child, childVO);
                        String key = systemId +"_"+ child.getTreeCode();
                        MidThreatResult midThreatResult = threatResultMap.get(key);
                        if (midThreatResult != null) {
                            childVO.setThreatFrequencyTag(midThreatResult.getThreatFrequencyTag());
                            childVO.setThreatFrequencyLevel(midThreatResult.getThreatFrequencyLevel());
                        }
                        list.add(childVO);
                    });
                }
            }
        });
        return list;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public Boolean deleteThreatTree(String threatTreeId) {
        // 先删除对应风险评估结果
        CoThreatAnalysis coThreatAnalysis = coThreatAnalysisMapper.selectById(threatTreeId);
        indicatorResultMapper.deleteByThreatAnalysis(coThreatAnalysis.getOperationId(), coThreatAnalysis.getBusSystem(), coThreatAnalysis.getLabelId());
        coThreatAnalysisMapper.deleteById(threatTreeId);
        return true;
    }

    @SchemaSwitch(value = ThreatFrequencyCalcDTO.class)
    @Override
    public List<ThreatTreeVO> autoCalcFrequency(ThreatFrequencyCalcDTO dto) {
        Long threatTemplateId = threatTreeMapper.selectThreatLibraryByOperationId(dto.getOperationId());
        ThreatTemplate threatTemplate = threatTemplateMapper.selectById(threatTemplateId);
        dto.getList().forEach(threatTreeVO -> {
            if (threatTemplate.getChildFlag() == null || !threatTemplate.getChildFlag()){
                calculateThreatFrequency(threatTreeVO, threatTemplate.getId());
            } else {
                threatTreeVO.getChildList().forEach(child -> calculateThreatFrequency(child,
                    threatTemplate.getId()));
            }
        });
        return dto.getList();
    }

    private void calculateThreatFrequency(ThreatTreeVO threatTree, Long threatTemplateId) {
        List<ThreatFrequency> threatFrequencyList = threatFrequencyMapper.selectList(
            new QueryWrapper<ThreatFrequency>().eq("threat_template_id", threatTemplateId).orderByDesc("frequency_level"));
        if (CollUtil.isEmpty(threatFrequencyList)){
            throw new ServiceException("威胁频率未配置！");
        }
        List<BigDecimal> frequencyList = new ArrayList<>();
        if (threatTree.getThreatLibraryIds() == null){
            // 若不存在，则返回列表最后一个结果
            ThreatFrequency threatFrequency = threatFrequencyList.get(threatFrequencyList.size()-1);
            threatTree.setThreatFrequencyTag(threatFrequency.getFrequencyTag());
            threatTree.setThreatFrequencyLevel(threatFrequency.getFrequencyLevel());
            return;
        }
        Arrays.asList(threatTree.getThreatLibraryIds()).forEach(threatLibraryId -> {
            // 根据威胁库获取威胁事件发生时间列表
            List<String> eventTimeList = threatEventMapper.getThreatEventByThreatLibraryId(threatLibraryId);
            FrequencyUtil frequencyUtil = new FrequencyUtil(threatFrequencyList);
            frequencyList.add(frequencyUtil.getThreatFrequencyLevel(eventTimeList));
        });
        Map<BigDecimal, String> frequencyMap = threatFrequencyList.stream()
            .collect(Collectors.toMap(ThreatFrequency::getFrequencyLevel, ThreatFrequency::getFrequencyTag));
        // 取最大值对应的威胁频率
        Optional<BigDecimal> optional = frequencyList.stream().max(Comparator.comparing(BigDecimal::doubleValue));
        if (optional.isPresent()){
            // 若不包含该频率等级，则获取最小频率等级
            if (!frequencyMap.containsKey(optional.get())){
                ThreatFrequency threatFrequency = threatFrequencyList.get(threatFrequencyList.size()-1);
                threatTree.setThreatFrequencyTag(threatFrequency.getFrequencyTag());
                threatTree.setThreatFrequencyLevel(threatFrequency.getFrequencyLevel());
                return;
            }
            threatTree.setThreatFrequencyTag(frequencyMap.get(optional.get()));
            threatTree.setThreatFrequencyLevel(optional.get());
            return;
        }
        // 若不存在，则返回列表最后一个结果
        ThreatFrequency threatFrequency = threatFrequencyList.get(threatFrequencyList.size()-1);
        threatTree.setThreatFrequencyTag(threatFrequency.getFrequencyTag());
        threatTree.setThreatFrequencyLevel(threatFrequency.getFrequencyLevel());
    }

    @SchemaSwitch(String.class)
    @Override
    public List<ThreatFrequency> getThreatFrequency(String operationId) {
        Long threatTemplateId = threatTreeMapper.selectThreatLibraryByOperationId(operationId);
        return threatFrequencyMapper.selectList(
            new QueryWrapper<ThreatFrequency>().eq("threat_template_id", threatTemplateId).orderByDesc("frequency_level"));
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    @SchemaSwitch(value = AddThreatAnalysisDTO.class)
    public void addMidThreatResult(AddThreatAnalysisDTO dto) {
        List<MidThreatResult> result = new ArrayList<>();
        try {
            add(dto);

            dto.getSystemId().forEach(systemId -> {
                // 先删后新增
                iMidThreatResultService.remove(
                    new QueryWrapper<MidThreatResult>().eq("operation_id", dto.getOperationId())
                        .eq("system_id", systemId));
                if (Boolean.TRUE.equals(dto.getHasChild())) {
                    dto.getThreatTreeList().forEach(parent -> parent.getChildList().forEach(child -> {
                        MidThreatResult midThreatResult = new MidThreatResult();
                        midThreatResult.setThreatFrequencyLevel(child.getThreatFrequencyLevel());
                        midThreatResult.setThreatFrequencyTag(child.getThreatFrequencyTag());
                        midThreatResult.setOperationId(dto.getOperationId());
                        midThreatResult.setSystemId(systemId);
                        midThreatResult.setThreatChild(child.getTreeCode());
                        //                                midThreatResult.setProcessTag();
                        midThreatResult.setThreatType(parent.getTreeCode());
                        result.add(midThreatResult);
                    }));
                } else {
                    dto.getThreatTreeList().forEach(parent -> {
                        MidThreatResult midThreatResult = new MidThreatResult();
                        midThreatResult.setThreatFrequencyLevel(parent.getThreatFrequencyLevel());
                        midThreatResult.setThreatFrequencyTag(parent.getThreatFrequencyTag());
                        midThreatResult.setOperationId(dto.getOperationId());
                        midThreatResult.setSystemId(systemId);
                        midThreatResult.setThreatChild(parent.getTreeCode());
                        midThreatResult.setThreatType(parent.getTreeCode());
                        result.add(midThreatResult);
                    });
                }
            });
            iMidThreatResultService.saveBatch(result);
        } catch (Exception e){
            log.error("Failed to save!", e);
        }
    }

    @SchemaSwitch(value = AddThreatAnalysisDTO.class)
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void editMidThreatResult(AddThreatAnalysisDTO dto) {
        List<MidThreatResult> result = new ArrayList<>();
        try {
            edit(dto);

            dto.getSystemId().forEach(systemId -> {
                // 先删后新增
                iMidThreatResultService.remove(
                    new QueryWrapper<MidThreatResult>().eq("operation_id", dto.getOperationId())
                        .eq("system_id", systemId));
                if (Boolean.TRUE.equals(dto.getHasChild())) {
                    dto.getThreatTreeList().forEach(parent -> parent.getChildList().forEach(child -> {
                        MidThreatResult midThreatResult = new MidThreatResult();
                        midThreatResult.setThreatFrequencyLevel(child.getThreatFrequencyLevel());
                        midThreatResult.setThreatFrequencyTag(child.getThreatFrequencyTag());
                        midThreatResult.setOperationId(dto.getOperationId());
                        midThreatResult.setSystemId(systemId);
                        midThreatResult.setThreatChild(child.getTreeCode());
                        midThreatResult.setThreatType(parent.getTreeCode());
                        result.add(midThreatResult);
                    }));
                } else {
                    dto.getThreatTreeList().forEach(parent -> {
                        MidThreatResult midThreatResult = new MidThreatResult();
                        midThreatResult.setThreatFrequencyLevel(parent.getThreatFrequencyLevel());
                        midThreatResult.setThreatFrequencyTag(parent.getThreatFrequencyTag());
                        midThreatResult.setOperationId(dto.getOperationId());
                        midThreatResult.setSystemId(systemId);
                        midThreatResult.setThreatChild(parent.getTreeCode());
                        midThreatResult.setThreatType(parent.getTreeCode());
                        result.add(midThreatResult);
                    });
                }
            });
            iMidThreatResultService.saveOrUpdateBatch(result);
        } catch (Exception e){
            log.error("Failed to save!", e);
        }
    }

    @Override
    public void batchUpdateFrequency(String operationId, String frequency) {
        MidThreatResult result = new MidThreatResult();
        iMidThreatResultService.update(result, new QueryWrapper<MidThreatResult>().eq("operation_id", operationId));
    }

    @Override
    public void deleteTreeChild(Long id) {
        iMidThreatResultService.removeById(id);
    }

    @Override
    public void batchUpdateSelectedFrequency(BatchUpdateFrequencyDTO dto) {
        dto.getIds().forEach(id -> {
            MidThreatResult result = new MidThreatResult();
            result.setId(id);
            result.setThreatFrequencyTag(dto.getThreatFrequencyTag());
            result.setThreatFrequencyLevel(dto.getThreatFrequencyLevel());
            iMidThreatResultService.updateById(result);
        });
    }

    @Override
    public void checkBusSystem(BusSystemCheckDTO dto) {
        for (int i=0; i<dto.getSystemIds().size(); i++){
            if (coInventoryMapper.selectCount(new QueryWrapper<CoInventory>().eq("system_id", dto.getSystemIds().get(i)).eq("operation_id", dto.getOperationId())) > 0) {
                continue;
            }
            throw new ServiceException(dto.getBusSystem().get(i) + "系统未检测到相关联的资产，因此无法进行风险值的计算。请检查资产盘点并重新尝试");
        }
    }
}
