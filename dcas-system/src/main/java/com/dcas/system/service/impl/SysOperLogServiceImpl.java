package com.dcas.system.service.impl;

import java.util.List;
import java.util.Objects;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.enums.LogSearchTypeEnum;
import com.dcas.common.enums.LogType;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.utils.PageResult;
import com.dcas.common.model.req.LogSearchReq;
import com.dcas.common.model.vo.OperationLogVO;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.dcas.common.domain.entity.SysOperLog;
import com.dcas.common.mapper.SysOperLogMapper;
import com.dcas.system.service.ISysOperLogService;

/**
 * 操作日志 服务层处理
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SysOperLogServiceImpl implements ISysOperLogService {

    private final SysOperLogMapper operLogMapper;

    /**
     * 新增操作日志
     *
     * @param operLog 操作日志对象
     */
    @Override
    public void insertOperlog(SysOperLog operLog) {
        operLogMapper.insertOperlog(operLog);
    }

    /**
     * 查询系统操作日志集合
     *
     * @return 操作日志集合
     * @param logType
     */
    @Override
    public List<OperationLogVO> selectOperLogList(LogType logType) {
        return operLogMapper.selectOperLogList(logType.name());
    }

    /**
     * 查询操作日志详细
     *
     * @param operId 操作ID
     * @return 操作日志对象
     */
    @Override
    public OperationLogVO selectOperLogById(Long operId) {
        OperationLogVO operationLogVO = operLogMapper.selectOperLogById(operId);
        if (Objects.isNull(operationLogVO)) {
            throw new ServiceException("日志信息不存在");
        }
        return operationLogVO;
    }

    /**
     * 清空操作日志
     */
    @Override
    public void cleanLog(DateTime datetime) {
        QueryWrapper<SysOperLog> query = new QueryWrapper<>();
        query.lt("oper_time", datetime);
        operLogMapper.delete(query);
    }

    @Override
    public PageResult<SysOperLog> pageOperLog(LogSearchReq logSearchReq) {
        Page<Object> page = PageHelper.startPage(logSearchReq.getCurrentPage(), logSearchReq.getPageSize());
        QueryWrapper<SysOperLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("log_type", logSearchReq.getLogType().name());
        queryWrapper.orderByDesc("oper_time");
        if (logSearchReq.getType() == LogSearchTypeEnum.ACCOUNT){
            queryWrapper.like("account", logSearchReq.getValue());
        } else if (logSearchReq.getType() == LogSearchTypeEnum.IP){
            queryWrapper.eq("oper_ip", logSearchReq.getValue());
        } else if (logSearchReq.getType() == LogSearchTypeEnum.TYPE){
            queryWrapper.eq("business_type", Integer.parseInt(logSearchReq.getValue()));
        } else if (logSearchReq.getType() == LogSearchTypeEnum.CONTENT){
            queryWrapper.like("title", logSearchReq.getValue());
        } else if (logSearchReq.getType() == LogSearchTypeEnum.STATUS){
            queryWrapper.eq("status", Integer.parseInt(logSearchReq.getValue()));
        } else if (logSearchReq.getType() == LogSearchTypeEnum.DETAIL){
            queryWrapper.like("error_msg", logSearchReq.getValue());
        } else if (logSearchReq.getType() == LogSearchTypeEnum.MODULE){
            queryWrapper.eq("module", logSearchReq.getValue());
        } else if (logSearchReq.getType() == LogSearchTypeEnum.NAME){
            queryWrapper.like("oper_name", logSearchReq.getValue());
        } else if (logSearchReq.getType() == LogSearchTypeEnum.TIME){
            queryWrapper.between("oper_time", logSearchReq.getStartDate(), logSearchReq.getEndDate());
        }
        List<SysOperLog> list = operLogMapper.selectList(queryWrapper);
        return PageResult.ofPage(page.getTotal(), list);
    }


}
