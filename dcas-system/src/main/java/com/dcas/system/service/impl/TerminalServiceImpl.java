package com.dcas.system.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.enums.AttributeEnum;
import com.dcas.common.enums.SysConfigEnum;
import com.dcas.common.domain.entity.CoLicense;
import com.dcas.common.domain.entity.SysConfig;
import com.dcas.common.model.other.Activecode;
import com.dcas.common.mapper.CoLicenseMapper;
import com.dcas.common.utils.ip.IpUtils;
import com.dcas.system.service.ISysConfigService;
import com.dcas.system.service.TerminalService;
import com.mchz.dcas.client.DcasCocClient;
import com.mchz.dcas.client.model.request.TerminalHeartRequest;
import com.mchz.dcas.client.model.request.TerminalMajorRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/7/7 14:08
 * @since 1.1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TerminalServiceImpl implements TerminalService {
    private static final Pattern pattern = Pattern.compile("ifcfg-(\\w+)");

    private final DcasCocClient dcasCocClient;

    private final CoLicenseMapper coLicenseMapper;
    private final ISysConfigService sysConfigService;
    @Value("${safety.net.script-path}")
    private String ethName;

    @Override
    public TerminalHeartRequest buildHeartRequest(int type) {
        TerminalHeartRequest req = new TerminalHeartRequest();
        req.setType(type);
        SysConfig config = sysConfigService.selectConfigById(SysConfigEnum.MACHINE_CODE.getCode());
        req.setMachineCode(config.getConfigValue());
        SysConfig config1 = sysConfigService.selectConfigById(SysConfigEnum.TERMINAL_CODE.getCode());
        req.setTerminalCode(config1.getConfigValue());
        QueryWrapper<CoLicense> wrapper = new QueryWrapper<>();
        wrapper.eq("device_id", config.getConfigValue());
        List<CoLicense> licenseList = coLicenseMapper.selectList(wrapper);

        List<TerminalHeartRequest.GrantDetail> grantDetails = new ArrayList<>();
        for (CoLicense license : licenseList) {
            if (StrUtil.isEmpty(license.getAttribute() )){
                continue;
            }
            if (AttributeEnum.CLIENT_GRANT.getAttribute().equals(license.getAttribute())){
                req.setName(license.getCustomName());
                if (Objects.equals(license.getActiveCode(), Activecode.SUCCESS.getCode())) {
                    req.setStatus((byte)1);
                } else if (Objects.equals(license.getActiveCode(), Activecode.EXPIRE.getCode())) {
                    req.setStatus((byte)2);
                } else {
                    req.setStatus((byte)0);
                }
                req.setActivationTime(license.getActivationTime());
                req.setStartTime(DateUtil.date(license.getAuthorTimeStart() * 1000));
                req.setEndTime(DateUtil.date(license.getAuthorTimeEnd() * 1000));

                long s = System.currentTimeMillis();
                Matcher matcher = pattern.matcher(ethName);
                String name = matcher.find() ? matcher.group(1) : StrUtil.EMPTY;
                req.setIntranetIp(IpUtils.getIpAddrByName(name));
                log.info("获取本机IP地址耗时：{}ms", System.currentTimeMillis() - s);
                String publicIPAddress = null;
                try {
                    long s1 = System.currentTimeMillis();
                    publicIPAddress = getPublicIPAddress();
                    log.info("获取公网IP地址耗时：{}ms", System.currentTimeMillis() - s1);
                } catch (Exception e) {
                    log.error("获取公网IP地址失败", e.getMessage());
                }
                req.setPublicIp(publicIPAddress);
                req.setLncType(license.getLncType());
            } else {
                TerminalHeartRequest.GrantDetail grantDetail = new TerminalHeartRequest.GrantDetail();
                grantDetail.setGrantType(license.getAttribute());
                if (Objects.equals(license.getActiveCode(), Activecode.SUCCESS.getCode())) {
                    grantDetail.setStatus(1);
                } else if (Objects.equals(license.getActiveCode(), Activecode.EXPIRE.getCode())) {
                    grantDetail.setStatus(2);
                } else {
                    grantDetail.setStatus(0);
                }
                grantDetail.setActivationTime(license.getActivationTime());
                grantDetail.setStartTime(DateUtil.date(license.getAuthorTimeStart() * 1000));
                grantDetail.setEndTime(DateUtil.date(license.getAuthorTimeEnd() * 1000));
                grantDetails.add(grantDetail);
            }

        }
        req.setGrantDetails(grantDetails);
        return req;
    }

    @Override
    public void majorGain() {
        TerminalMajorRequest request = new TerminalMajorRequest();
        SysConfig config = sysConfigService.selectConfigById(SysConfigEnum.MACHINE_CODE.getCode());
        request.setMachineCode(config.getConfigValue());
        SysConfig config1 = sysConfigService.selectConfigById(SysConfigEnum.TERMINAL_CODE.getCode());
        request.setTerminalCode(config1.getConfigValue());
        String terminalCode = dcasCocClient.majorGain(request);
        if (StrUtil.isNotEmpty(terminalCode)) {
            config1.setConfigValue(terminalCode);
            sysConfigService.updateConfig(config1);
            QueryWrapper<CoLicense> wrapper = new QueryWrapper<>();
            wrapper.eq("device_id", config.getConfigValue());
            wrapper.eq("attribute", AttributeEnum.CLIENT_GRANT.getAttribute());
            CoLicense license = coLicenseMapper.selectOne(wrapper);
            license.setExpired(Boolean.FALSE);
            license.setLocked(Boolean.FALSE);
            coLicenseMapper.updateById(license);
            log.info("抢占主权成功，终端码：{}", terminalCode);
        }
    }

    private String getPublicIPAddress() throws Exception {
        // 使用 ipify.org 的 API 服务来获取公网 IP 地址
        String ipifyApiUrl = "http://api.ipify.org?format=text";

        // 创建一个 URL 对象
        URL url = new URL(ipifyApiUrl);

        // 打开 URL 连接
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // 设置请求方法为 GET
        connection.setRequestMethod("GET");

        // 获取返回状态码
        int responseCode = connection.getResponseCode();

        // 如果状态码为 200，则获取返回内容
        if (responseCode == 200) {
            BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String inputLine;
            StringBuilder response = new StringBuilder();

            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }

            in.close();

            // 返回获取到的公网 IP 地址
            return response.toString();
        } else {
            throw new RuntimeException("获取公网地址失败，状态码: " + responseCode);
        }
    }
}
